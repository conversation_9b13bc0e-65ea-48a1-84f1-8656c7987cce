package com.nspace.group.module.logs.dal.mapper.offlinelog;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.nspace.group.framework.mybatis.core.mapper.BaseMapperX;
import com.nspace.group.module.logs.dal.dataobject.offlinelog.CDNOfflineLogInfoDO;
import com.nspace.group.module.logs.dal.dataobject.offlinelog.DelayedOfflineLogDO;
import com.nspace.group.module.logs.dal.dataobject.offlinelog.LiveOfflineLogInfoDO;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version :OfflineLogInfoMapper.java, v0.1 2024年12月17日 10:55 zhangxin Exp
 */
@Mapper
@DS("nspace_log")
public interface LiveOfflineLogInfoMapper extends BaseMapperX<LiveOfflineLogInfoDO> {

    List<DelayedOfflineLogDO> getHistoryLogs(String batchNum);

}