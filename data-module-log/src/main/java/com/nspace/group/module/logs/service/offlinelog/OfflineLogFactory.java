package com.nspace.group.module.logs.service.offlinelog;

import com.nspace.group.module.logs.dal.mapper.offlinelog.OfflineLogMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

@Component
public class OfflineLogFactory {
    @Resource
    private Map<String, OfflineLogFetcherService> fetcherServices;
    @Resource
    private Map<String, OfflineLogTransformerService> transformerServices;
    @Resource
    private Map<String, OfflinePersistService> persistServices;
    @Resource
    private Map<String, DelayedOfflineLogService> delayedServices;
    @Resource
    private Map<String,OfflineLogMapper> logTypeMapper;


    public OfflineLogFetcherService getFetcherService(String logType) {
        return fetcherServices.get(logType + "-offline-fetcher");
    }

    public OfflineLogTransformerService getTransformerService(String logType) {
        return transformerServices.get(logType + "-offline-transformer");
    }

    public OfflinePersistService getPersistService(String logType) {
        return persistServices.get(logType + "-offline-persist");
    }

    public DelayedOfflineLogService getDelayedService(String logType) {
        return delayedServices.get(logType + "-offline-delayed-fetcher");
    }

    public OfflineLogMapper getOfflineLogMapper(String logType) {
        return logTypeMapper.get(logType + "-offline-mapper");
    }


}
