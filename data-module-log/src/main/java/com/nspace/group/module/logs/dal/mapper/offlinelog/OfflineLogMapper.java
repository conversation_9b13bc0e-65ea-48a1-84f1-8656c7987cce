package com.nspace.group.module.logs.dal.mapper.offlinelog;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.nspace.group.module.logs.dal.dataobject.offlinelog.OfflineLogDO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@DS("nspace_log")
public interface OfflineLogMapper {


    @DS("nspace_controller")
    List<OfflineLogDO> selectByCurrent();

    @DS("nspace_analysis")
    Long getCurrentBatchCount(@Param("start") LocalDateTime start, @Param("end") LocalDateTime end
            , @Param("domain") String domain);

    Set<OfflineLogDO> selectFailedTask();

    List<OfflineLogDO> selectMergeTask(LocalDateTime time);
}
