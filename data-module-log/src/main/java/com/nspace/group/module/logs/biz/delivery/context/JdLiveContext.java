package com.nspace.group.module.logs.biz.delivery.context;

import cn.hutool.core.lang.Assert;
import com.nspace.group.module.infra.design.context.LogContext;
import com.nspace.group.module.logs.service.delivery.dto.LogDeliveryTaskDTO;

public class JdLiveContext extends LogContext {

    // 接口path
    private final String apiPath;

    // 接口基础Url
    private final String apiBaseUrl;

    // 当前投递任务
    private final LogDeliveryTaskDTO deliveryTask;

    public JdLiveContext(String targetType, Integer logLimit, Integer batchLimit, Integer retryCount,
                         String privateKey, String accessKey, String apiPath, String apiBaseUrl,
                         LogDeliveryTaskDTO deliveryTask) {
        super(targetType, logLimit, batchLimit, retryCount, privateKey, accessKey);
        Assert.notNull(deliveryTask);
        Assert.notBlank(apiPath);
        Assert.notBlank(apiBaseUrl);
        this.apiPath = apiPath;
        this.apiBaseUrl = apiBaseUrl;
        this.deliveryTask = deliveryTask;
    }

    public LogDeliveryTaskDTO getDeliveryTask() {
        return deliveryTask;
    }

    public String getApiPath() {
        return apiPath;
    }

    public String getApiBaseUrl() {
        return apiBaseUrl;
    }
}
