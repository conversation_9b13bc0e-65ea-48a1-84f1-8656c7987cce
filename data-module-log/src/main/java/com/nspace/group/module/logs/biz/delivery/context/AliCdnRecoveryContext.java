package com.nspace.group.module.logs.biz.delivery.context;

import cn.hutool.core.lang.Assert;
import com.nspace.group.module.infra.design.context.LogContext;
import com.nspace.group.module.logs.service.delivery.dto.DeliveryLogDetailDTO;

import java.util.List;

public class AliCdnRecoveryContext extends LogContext {

    private final String project;

    private final String logStore;

    //当前重试次数
    private Integer curRetryCount;

    // 当前日志条数
    private List<DeliveryLogDetailDTO> logDetails;

    public AliCdnRecoveryContext(String targetType, Integer logLimit, Integer batchLimit, Integer retryCount,
                                 String privateKey, String accessKey, String project, String logStore) {
        super(targetType, logLimit, batchLimit, retryCount, privateKey, accessKey);
        Assert.notBlank(project);
        Assert.notBlank(logStore);
        this.project = project;
        this.logStore = logStore;
    }

    public List<DeliveryLogDetailDTO> getLogDetails() {
        return logDetails;
    }

    public void setLogDetails(List<DeliveryLogDetailDTO> logDetails) {
        this.logDetails = logDetails;
    }

    public Integer getCurRetryCount() {
        return curRetryCount;
    }

    public void setCurRetryCount(Integer curRetryCount) {
        this.curRetryCount = curRetryCount;
    }

    public String getProject() {
        return project;
    }

    public String getLogStore() {
        return logStore;
    }
}
