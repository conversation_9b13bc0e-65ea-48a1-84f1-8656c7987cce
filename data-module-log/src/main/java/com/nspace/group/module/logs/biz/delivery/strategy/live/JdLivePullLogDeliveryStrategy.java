package com.nspace.group.module.logs.biz.delivery.strategy.live;

import cn.hutool.crypto.SecureUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.nspace.group.module.logs.biz.delivery.context.JdLiveContext;
import com.nspace.group.module.infra.design.context.LogContext;
import com.nspace.group.module.infra.design.strategy.LogDeliveryStrategy;
import com.nspace.group.module.logs.service.delivery.dto.LogDeliveryRecordDTO;
import com.nspace.group.module.logs.service.delivery.dto.LogDeliveryTaskDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;

@Slf4j
@Component
public class JdLivePullLogDeliveryStrategy implements LogDeliveryStrategy {

    // 投递任务Redis key过期时间（分钟）
    private final int TASK_KEY_EXPIRATION = 18;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private JdLivePullLogSendStrategy logSendStrategy;

    @Resource
    private JdLivePullLogCompletionStrategy logCompletionStrategy;

    @Override
    public void deliver(LogContext logContext) {
        JdLiveContext context = (JdLiveContext) logContext;
        LogDeliveryTaskDTO deliveryTask = context.getDeliveryTask();
        String taskKey = calculateDeliveryTaskKey(deliveryTask.getDeliveryRecord(), context.getTargetType());
        Boolean success = stringRedisTemplate.opsForValue().setIfAbsent(taskKey, StringPool.ONE, Duration.ofMinutes(TASK_KEY_EXPIRATION));
        if (Boolean.TRUE.equals(success)) {
            try {
                logSendStrategy.send(logContext);
                logCompletionStrategy.complete(logContext);
            } finally {
                log.info("remove_log_delivery_task_from_registry,task={}", taskKey);
                stringRedisTemplate.delete(taskKey);
            }
        } else {
            log.warn("log_delivery_task_already_exists,task={}", taskKey);
        }
    }

    private String calculateDeliveryTaskKey(LogDeliveryRecordDTO deliveryRecord, String targetType) {
        String keyBody = String.join(StringPool.EMPTY, targetType, deliveryRecord.getTenantId().toString(),
                deliveryRecord.getDomain(), deliveryRecord.getBizType(), deliveryRecord.getLogType(),
                deliveryRecord.getLogStartId().toString(), deliveryRecord.getLogEndId().toString(),
                deliveryRecord.getLogStartTime().toString(), deliveryRecord.getLogEndTime().toString());
        return String.join(StringPool.COLON, "log_delivery_task", SecureUtil.md5(keyBody));
    }
}
