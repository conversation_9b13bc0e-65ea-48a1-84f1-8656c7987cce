package com.nspace.group.module.logs.config.logs;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties("dataworks.logs.offline")
public class OfflineLogConfiguration {

    /**
     * 临时写的文件暂存在这个目录下,包括本地写的文件和从 oss 下载的文件
     */
    private String path = "/tmp/logs/offline";


    /**
     * csv 写入多少行,滚动生成新的文件
     */
    private Integer csvRows = 500000;

    /**
     * 批次,数据库中的行数大于多少才会分批次
     */
    private Integer batchSize = 500000;

    /**
     * 每次从数据库拉取的行数
     */
    private Integer fetchSize = 4096;

    /**
     * 每一批次需要执行的子批次数量,默认为12,需要可以被 60 整除
     */
    private Integer totalBatch = 12;


    /**
     * 目前doris 为 2.7 版本, 存在 datetimeV2 类型缺失时间分区问题
     * 所以需要增加 offset 弥补问题
     */
    private Integer offset = 16;
}
