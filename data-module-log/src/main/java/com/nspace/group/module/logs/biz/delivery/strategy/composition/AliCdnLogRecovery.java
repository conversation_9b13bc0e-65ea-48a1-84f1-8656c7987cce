package com.nspace.group.module.logs.biz.delivery.strategy.composition;

import com.nspace.group.module.infra.design.chain.DeliveryLogHandler;
import com.nspace.group.module.infra.design.chain.LogHandler;
import com.nspace.group.module.infra.design.strategy.LogChainComposeStrategy;
import com.nspace.group.module.logs.biz.delivery.strategy.cdn.AliRecoveryLogDeliveryStrategy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class AliCdnLogRecovery implements LogChainComposeStrategy {

    @Resource(name = "aliCdnRecoveryLogDeliveryStrategy")
    private AliRecoveryLogDeliveryStrategy recoveryLogDeliveryStrategy;

    @Override
    public LogHandler composeChain() {
        return new DeliveryLogHandler(recoveryLogDeliveryStrategy);
    }
}
