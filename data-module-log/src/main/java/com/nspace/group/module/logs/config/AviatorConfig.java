package com.nspace.group.module.logs.config;

import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.aviator.runtime.type.AviatorString;
import com.nspace.group.module.logs.utils.CacheComponent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.ContextRefreshedEvent;

import javax.annotation.Resource;
import java.util.Map;

@Configuration
public class AviatorConfig implements ApplicationListener<ContextRefreshedEvent> {

    @Resource
    private CacheComponent dateCache;


    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        AviatorEvaluator.addFunction(new AbstractFunction() {
            @Override
            public String getName() {
                return "ISO8061";
            }

            @Override
            public AviatorObject call(Map<String, Object> env, AviatorObject dateObj) {
                String date = (String) dateObj.getValue(env);
                return new AviatorString(dateCache.getISO8601Date(date, "+08:00"));
            }

            @Override
            public AviatorObject call(Map<String, Object> env, AviatorObject dateObj, AviatorObject zoneObj) {
                String date = (String) dateObj.getValue(env);
                String zone = (String) zoneObj.getValue(env);
                return new AviatorString(dateCache.getISO8601Date(date, zone));
            }
        });
    }
}
