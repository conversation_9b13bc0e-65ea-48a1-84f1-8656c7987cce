package com.nspace.group.module.logs.service.offlinelog;

import com.nspace.group.framework.file.core.FileClient;
import com.nspace.group.module.logs.dal.dataobject.offlinelog.OfflineLogDO;
import com.nspace.group.module.logs.dal.mapper.offlinelog.OfflineLogMapper;
import com.nspace.group.module.logs.enums.ExportTypeEnum;
import com.nspace.group.module.logs.service.offlinelog.dto.OfflineLogDTO;
import com.nspace.group.module.logs.service.offlinelog.dto.TaskDTO;
import com.nspace.group.module.logs.utils.DTF;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

@Slf4j
@Service
public class OfflineLogServiceImpl implements OfflineLogService {


    @Resource
    private OfflineLogFactory offlineLogFactory;

    @Resource
    private OfflineLogGenerateService generateService;

    @Resource(name = "commonThreadPool")
    private Executor executors;

    @Resource
    private FileClient fileClient;


    @Override
    public String dealOfflineLog(TaskDTO taskDTO) {
        Integer offset = taskDTO.getOffset();
        String logType = taskDTO.getLogType();


        LocalDateTime now = LocalDateTime.now();
        LocalDateTime dateTime = now.minusHours(offset);
        String batchNum = dateTime.format(DTF.yyyyMMddHH);

        OfflineLogMapper logMapper = offlineLogFactory.getOfflineLogMapper(logType);
        Collection<OfflineLogDO> offlineLogDOS = logMapper.selectByCurrent();

        //创建 bucket lifecycle
        List<Integer> days = offlineLogDOS.stream().map(OfflineLogDO::getLifeCycle)
                .distinct().collect(Collectors.toList());
        try {
            fileClient.setBucketExpiration(logType, days);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        //获取当前批次需要执行的任务
        offlineLogDOS.stream()
                .filter(item -> StringUtils.isNoneBlank(item.getDomain()))
                .map(OfflineLogDTO::from)
                .forEach(logDO -> {
                    executors.execute(() -> {
                        logDO.setBatchNum(batchNum);
                        generateService.generateOfflineLog(logDO, false);
                    });
                });
        //获取历史任务中执行失败的子批次
        offlineLogDOS = logMapper.selectFailedTask();
        offlineLogDOS.stream()
                .map(item -> {
                    OfflineLogDTO dto = OfflineLogDTO.from(item);
                    dto.setRetryCount(item.getRetryCount() + 1);
                    return dto;
                })
                .forEach(logDO -> {
                    executors.execute(() -> {
                        generateService.generateOfflineLog(logDO, true);
                    });
                });
        return "SUCCESS";
    }

    @Override
    public String dealDelayedOfflineLog(TaskDTO taskDTO) {
        Integer offset = taskDTO.getOffset();
        String logType = taskDTO.getLogType();

        LocalDateTime now = LocalDateTime.now();
        LocalDateTime dateTime = now.minusHours(offset);
        String batchNum = dateTime.format(DTF.yyyyMMddHH);
        //查询 doris 数据 和 导出的日志数据是否存在差异,则会进行补充

        DelayedOfflineLogService delayedService = offlineLogFactory.getDelayedService(logType);
        List<OfflineLogDTO> delayedLogs = delayedService.getDelayedLogs(batchNum);
        delayedLogs.forEach(delayedDTO -> {
            if (Boolean.TRUE.equals(delayedDTO.isRefresh())) {
                //增量数据的总批次数为1
                delayedDTO.setTotalBatch(1);
                delayedDTO.setExportType(ExportTypeEnum.INCREMENT);
                executors.execute(() -> {
                    generateService.generateOfflineLog(delayedDTO, false);
                });
            } else if (Boolean.TRUE.equals(delayedDTO.isSyncAll())) {
                executors.execute(() -> {
                    generateService.generateOfflineLog(delayedDTO, true);
                });
            }
        });
        return "SUCCESS";
    }

    @Override
    public String dealMergeFilesJob(TaskDTO taskDTO) {
        Integer offset = taskDTO.getOffset();
        String logType = taskDTO.getLogType();
        LocalDateTime dateTime = LocalDateTime.now().minusHours(offset);
        OfflineLogMapper logMapper = offlineLogFactory.getOfflineLogMapper(logType);
        List<OfflineLogDO> offlineLogDOS = logMapper.selectMergeTask(dateTime);
        offlineLogDOS.stream()
                .filter(item -> item.getLogType().equals(logType))
                .map(OfflineLogDTO::from)
                .forEach(dto -> executors.execute(() -> {
                    generateService.mergeSubBatch(dto);
                }));
        OfflinePersistService persistService = offlineLogFactory.getPersistService(logType);
        int rows = persistService.cleanExpiredLogs();
        log.debug("Clean {}rows expired logs successfully", rows);
        return "SUCCESS";
    }
}
