package com.nspace.group.module.logs.utils;

import cn.hutool.core.thread.ExecutorBuilder;
import cn.hutool.core.thread.ThreadFactoryBuilder;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;

public class Constant {

    public static final String[] LIVE_FIELDS = new String[]{
            "request_id", "log_time", "duration", "stream_protocol", "duration", "total_duration", "client_addr",
            "server_addr", "scheme", "http_method", "domain", "request_uri", "status", "bytes_sent", "interval_bytes_sent",
            "bytes_sent", "interval_bytes_recv", "bytes_recv", "connect_time", "first_byte_recv_time", "server_protocol",
            "http_method", "referer", "user_agent", "err", "discontinuous_count",
            "discontinuous_time", "round", "end", "cur_timestamp"
    };

    public static final Integer LIVE_FIELDS_SIZE = LIVE_FIELDS.length - 3;

    private static final String[] CDN_FIELDS = new String[]{
            "log_time", "request_id", "client_addr", "server_addr", "http_method", "scheme",
            "domain", "request_uri", "uri_param", "server_protocol", "status", "cache_status", "http_referer",
            "http_response_range", "content_type", "response_size", "body_bytes_sent", "cache_level",
            "user_agent", "http_request_range", "sent_http_location", "sent_http_content_length", "remote_ip",
            "last_modified", "http_content_encoding", "http_accept", "quic", "cur_timestamp"
    };


    public static final Integer CDN_FIELDS_SIZE = LIVE_FIELDS.length - 1;

    public static final String LIVE_SELECT_FIELDS = String.join(",", LIVE_FIELDS);

    public static final String CDN_SELECT_FIELDS = String.join(",", CDN_FIELDS);

    public static final ExecutorService COMPRESSOR_EXECUTORS = ExecutorBuilder.create()
            .setCorePoolSize(5).setMaxPoolSize(10).setWorkQueue(new ArrayBlockingQueue<>(10000))
            .setThreadFactory(ThreadFactoryBuilder.create().setNamePrefix("gzip-compressor-").build())
            .setHandler(new ThreadPoolExecutor.CallerRunsPolicy())
            .build();


    public static final ExecutorService IO_EXECUTORS = ExecutorBuilder.create()
            .setCorePoolSize(5).setMaxPoolSize(10).setWorkQueue(new ArrayBlockingQueue<>(10000))
            .setThreadFactory(ThreadFactoryBuilder.create().setNamePrefix("io-executors-").build())
            .setHandler(new ThreadPoolExecutor.CallerRunsPolicy())
            .build();


    public static final String SUCCESS = "SUCCESS";


    public static final String GZIP = "application/x-gzip";
}
