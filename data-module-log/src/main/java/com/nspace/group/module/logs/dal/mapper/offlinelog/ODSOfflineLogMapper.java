package com.nspace.group.module.logs.dal.mapper.offlinelog;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.nspace.group.module.logs.dal.dataobject.offlinelog.DelayedOfflineLogDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
@DS("nspace_analysis")
public interface ODSOfflineLogMapper {


    List<DelayedOfflineLogDO> selectCDNDelayedLogs(@Param("start") LocalDateTime startTime, @Param("end") LocalDateTime endTime
            , @Param("logs") List<DelayedOfflineLogDO> delayedLogs);

    List<DelayedOfflineLogDO> selectLiveDelayedLogs(@Param("start") LocalDateTime startTime, @Param("end") LocalDateTime endTime
            , @Param("logs") List<DelayedOfflineLogDO> delayedLogs);
}
