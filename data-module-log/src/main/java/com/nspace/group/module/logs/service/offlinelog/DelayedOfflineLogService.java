package com.nspace.group.module.logs.service.offlinelog;

import com.nspace.group.module.logs.service.offlinelog.dto.OfflineLogDTO;

import java.util.List;

public interface DelayedOfflineLogService {


    /**
     * 对比这一批次的数据是否存在明显差异,如果存在差异,则需要补充数据,分两种情况
     * 1. timestamp 之前的数据存在差异,则需要补充全量数据
     * 2. timestamp 之后的数据存在差异,则需要补充增量数据
     *
     * @param batchNum 批次
     * @return 返回 dto 体列表
     */

    List<OfflineLogDTO> getDelayedLogs(String batchNum);
}
