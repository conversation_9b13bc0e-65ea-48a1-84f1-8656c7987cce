package com.nspace.group.module.logs.service.offlinelog.live;

import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Expression;
import com.nspace.group.module.logs.service.offlinelog.AbstractOfflineLogTransformerService;
import lombok.SneakyThrows;
import org.springframework.stereotype.Service;

import java.sql.ResultSet;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

import static com.nspace.group.module.logs.utils.Constant.LIVE_FIELDS_SIZE;

@Service("live-offline-transformer")
public class LiveOfflineLogTransformerServiceImpl extends AbstractOfflineLogTransformerService {

    private final Map<Integer, String> defaultExpressions;

    {
        defaultExpressions = new HashMap<>();
        defaultExpressions.put(1, "ISO8061(param[1])");
        defaultExpressions.put(18, "ISO8061(param[18])");
    }


    @Override
    public Map<Integer, Expression> getDefaultExpressions() {
        return defaultExpressions.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey
                , item -> AviatorEvaluator.compile(item.getValue(), true)));
    }

    @Override
    @SneakyThrows
    public String[] transform0(ResultSet resultSet) {
        String[] result = new String[LIVE_FIELDS_SIZE + 1];
        for (int i = 0; i < LIVE_FIELDS_SIZE; i++) {
            result[i] = String.valueOf(resultSet.getObject(i + 1));
        }
        int round = resultSet.getInt("round");
        int end = resultSet.getInt("end");
        /**
         * stream_status=1 对应的round=1 end=1
         * stream_status=-1 对应的round=0 end=0
         * 其他场景 round=1 end=0
         */
        if (round == 1) {
            result[LIVE_FIELDS_SIZE] = "1";
        } else if (end == 1) {
            result[LIVE_FIELDS_SIZE] = "-1";
        } else if (round == 0) {
            result[LIVE_FIELDS_SIZE] = "0";
        }
        return result;
    }

}
