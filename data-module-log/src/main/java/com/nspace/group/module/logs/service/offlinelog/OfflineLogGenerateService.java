package com.nspace.group.module.logs.service.offlinelog;


import com.nspace.group.module.logs.service.offlinelog.dto.OfflineLogDTO;

/**
 * <AUTHOR>
 * @version :OfflineLogGenerateService.java, v0.1 2024年12月18日 11:52 Exp
 */
public interface OfflineLogGenerateService {

    /**
     * 生成离线日志并上传到 oss 中
     *
     * @param dto          离线日志DTO 实体
     * @param removeOldLog 是否删除旧的任务
     */
    void generateOfflineLog(OfflineLogDTO dto, boolean removeOldLog);


    /**
     * 合并子批次到一个批次中
     *
     * @param dto 离线日志DTO 实体
     */
    void mergeSubBatch(OfflineLogDTO dto);
}
