package com.nspace.group.module.logs.biz.delivery.strategy.live;

import com.nspace.group.framework.common.enums.BusinessTypeEnum;
import com.nspace.group.module.logs.biz.delivery.context.JdLiveContext;
import com.nspace.group.module.infra.design.context.LogContext;
import com.nspace.group.module.infra.design.strategy.LogCompletionStrategy;
import com.nspace.group.module.logs.service.delivery.LogDeliveryDetailService;
import com.nspace.group.module.logs.service.delivery.dto.LogDeliveryRecordDTO;
import com.nspace.group.module.logs.service.delivery.dto.LogDeliveryTaskDTO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class JdLivePullLogCompletionStrategy implements LogCompletionStrategy {

    @Resource
    private LogDeliveryDetailService logDeliveryDetailService;

    @Override
    public void complete(LogContext logContext) {
        JdLiveContext context = (JdLiveContext) logContext;
        LogDeliveryTaskDTO deliveryTask = context.getDeliveryTask();
        LogDeliveryRecordDTO deliveryRecord = deliveryTask.getDeliveryRecord();
        //保存日志投递明细
        logDeliveryDetailService.saveMany(BusinessTypeEnum.BUSINESS_TYPE_LSS.getCode(), deliveryRecord.getDomain(), deliveryRecord.getDeliveryStatus(), deliveryTask.getLogIdTimeMap());
    }
}
