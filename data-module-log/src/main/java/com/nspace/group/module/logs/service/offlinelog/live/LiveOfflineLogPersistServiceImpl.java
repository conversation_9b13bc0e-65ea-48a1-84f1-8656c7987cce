package com.nspace.group.module.logs.service.offlinelog.live;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.nspace.group.module.logs.dal.dataobject.offlinelog.*;
import com.nspace.group.module.logs.dal.mapper.offlinelog.LiveOfflineLogDetailInfoMapper;
import com.nspace.group.module.logs.dal.mapper.offlinelog.LiveOfflineLogInfoMapper;
import com.nspace.group.module.logs.enums.OfflineStatusEnum;
import com.nspace.group.module.logs.service.offlinelog.OfflinePersistService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;


@Service("live-offline-persist")
public class LiveOfflineLogPersistServiceImpl implements OfflinePersistService {

    @Resource
    private LiveOfflineLogInfoMapper liveOfflineLogMapper;

    @Resource
    private LiveOfflineLogDetailInfoMapper detailInfoMapper;

    @Override
    public void persist(OfflineLogInfoDO logDO) {
        if (logDO instanceof OfflineLogDetailInfoDO) {
            LiveOfflineLogDetailInfoDO detailDO = new LiveOfflineLogDetailInfoDO();
            BeanUtils.copyProperties(logDO, detailDO);
            if (logDO.getId() == null) {
                detailInfoMapper.insert(detailDO);
            } else {
                detailInfoMapper.updateById(detailDO);
            }
            logDO.setId(detailDO.getId());
        } else if (logDO != null) {
            LiveOfflineLogInfoDO infoDO = new LiveOfflineLogInfoDO();
            BeanUtils.copyProperties(logDO, infoDO);
            if (logDO.getId() == null) {
                liveOfflineLogMapper.insert(infoDO);
            } else {
                liveOfflineLogMapper.updateById(infoDO);
            }
            logDO.setId(infoDO.getId());
        }

    }

    @Override
    public void batchUpdate(List<Long> ids, String message, OfflineStatusEnum status, List<Long> detailIds) {
        LambdaUpdateWrapper<LiveOfflineLogInfoDO> wrapper = new UpdateWrapper<LiveOfflineLogInfoDO>().lambda()
                .set(LiveOfflineLogInfoDO::getStatus, status)
                .set(LiveOfflineLogInfoDO::getMessage, message)
                .in(LiveOfflineLogInfoDO::getId, ids);
        liveOfflineLogMapper.update(wrapper);
        if (CollUtil.isNotEmpty(detailIds)) {
            detailInfoMapper.deleteBatchIds(detailIds);
        }
    }

    @Override
    public void batchUpdateDetails(List<Long> ids, String message, OfflineStatusEnum status) {
        LambdaUpdateWrapper<LiveOfflineLogDetailInfoDO> wrapper = new UpdateWrapper<LiveOfflineLogDetailInfoDO>().lambda()
                .set(LiveOfflineLogDetailInfoDO::getStatus, status)
                .set(LiveOfflineLogDetailInfoDO::getMessage, message)
                .in(LiveOfflineLogDetailInfoDO::getId, ids);
        detailInfoMapper.update(wrapper);
    }

    @Override
    public void remove(String batchNum, Long tenantId, String domain, Integer executeBatch) {
        LambdaQueryWrapper<LiveOfflineLogInfoDO> wrapper = Wrappers.lambdaQuery(LiveOfflineLogInfoDO.class)
                .eq(LiveOfflineLogInfoDO::getBatchNum, batchNum)
                .eq(LiveOfflineLogInfoDO::getTenantId, tenantId)
                .eq(LiveOfflineLogInfoDO::getDomain, domain);
        liveOfflineLogMapper.delete(wrapper);

        LambdaQueryWrapper<LiveOfflineLogDetailInfoDO> detailWrapper = Wrappers.lambdaQuery(LiveOfflineLogDetailInfoDO.class)
                .eq(LiveOfflineLogDetailInfoDO::getBatchNum, batchNum)
                .eq(LiveOfflineLogDetailInfoDO::getTenantId, tenantId)
                .eq(LiveOfflineLogDetailInfoDO::getDomain, domain)
                .eq(executeBatch != null, LiveOfflineLogDetailInfoDO::getExecuteBatch, executeBatch);
        detailInfoMapper.delete(detailWrapper);
    }

    @Override
    public List<? extends OfflineLogDetailInfoDO> find(String batchNum, Long tenantId, String domain) {
        LambdaQueryWrapper<LiveOfflineLogDetailInfoDO> wrapper = Wrappers.lambdaQuery(LiveOfflineLogDetailInfoDO.class)
                .eq(LiveOfflineLogDetailInfoDO::getBatchNum, batchNum)
                .eq(LiveOfflineLogDetailInfoDO::getTenantId, tenantId)
                .eq(LiveOfflineLogDetailInfoDO::getDomain, domain);
        return detailInfoMapper.selectList(wrapper);
    }

    @Override
    public int cleanExpiredLogs() {
        LocalDateTime startOfDay = LocalDateTime.now().with(LocalTime.MIN);
        QueryWrapper<LiveOfflineLogInfoDO> wrapper = Wrappers.query(LiveOfflineLogInfoDO.class)
                .isNotNull("lifecycle")
                .apply("create_time + CAST(lifecycle || ' days' AS interval) < {0}", startOfDay);
        return liveOfflineLogMapper.delete(wrapper);
    }


}
