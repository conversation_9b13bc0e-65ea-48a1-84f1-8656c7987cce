package com.nspace.group.module.logs.biz.delivery;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.nspace.group.module.infra.design.chain.LogHandler;
import com.nspace.group.module.logs.biz.delivery.context.AliCdnRecoveryContext;
import com.nspace.group.module.logs.biz.delivery.context.JdLiveRecoveryContext;
import com.nspace.group.module.logs.biz.delivery.strategy.composition.AliCdnLogRecovery;
import com.nspace.group.module.logs.biz.delivery.strategy.composition.JdLiveLogRecovery;
import com.nspace.group.module.logs.convert.delivery.LogDeliveryConvert;
import com.nspace.group.module.logs.service.delivery.dto.DeliveryLogRecoveryConfigDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service(value = "deliveryLogRecoveryService")
public class DeliveryLogRecoveryServiceImpl implements DeliveryLogRecoveryService {

    @Resource
    private AliCdnLogRecovery aliCdnLogRecovery;

    @Resource
    private JdLiveLogRecovery jdLiveLogRecovery;

    @Override
    public void recoverJdLiveLogs(DeliveryLogRecoveryConfigDTO configDTO) {
        String targetType = configDTO.getTargetType();

        try {
            LogHandler logHandler = jdLiveLogRecovery.composeChain();
            JdLiveRecoveryContext logContext = LogDeliveryConvert.INSTANCE.getJdLiveRecoveryContext(configDTO);
            logHandler.handle(logContext);
        } catch (Exception e) {
            log.error("recoverJdLiveLogs,unknown_exception,target_type={},error={}",
                    targetType, ExceptionUtil.getRootCauseMessage(e));
        }
    }

    @Override
    public void recoverAliCdnLogs(DeliveryLogRecoveryConfigDTO configDTO) {
        String targetType = configDTO.getTargetType();

        try {
            LogHandler logHandler = aliCdnLogRecovery.composeChain();
            AliCdnRecoveryContext logContext = LogDeliveryConvert.INSTANCE.getAliCdnRecoveryContext(configDTO);
            logHandler.handle(logContext);
        } catch (Exception e) {
            log.error("recoverAliCdnLogs,unknown_exception,target_type={},error={}",
                    targetType, ExceptionUtil.getRootCauseMessage(e));
        }
    }
}
