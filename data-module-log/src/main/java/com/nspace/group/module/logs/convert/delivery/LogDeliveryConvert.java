package com.nspace.group.module.logs.convert.delivery;

import com.nspace.group.module.infra.service.user.dto.LogDeliveryDomainDTO;
import com.nspace.group.module.logs.biz.delivery.context.AliCdnRecoveryContext;
import com.nspace.group.module.logs.biz.delivery.context.JdLiveContext;
import com.nspace.group.module.logs.biz.delivery.context.JdLiveRecoveryContext;
import com.nspace.group.module.logs.service.delivery.dto.DeliveryLogRecoveryConfigDTO;
import com.nspace.group.module.logs.service.delivery.dto.LogDeliveryParamDTO;
import com.nspace.group.module.logs.service.delivery.dto.LogDeliveryTaskDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @version :LogDeliveryConvert.java, v0.1 2025年03月19日 10:21 zhangxin Exp
 */
@Mapper
public interface LogDeliveryConvert {

    LogDeliveryConvert INSTANCE = Mappers.getMapper(LogDeliveryConvert.class);

    @Mapping(target = "privateKey", source = "deliveryDomain.sk")
    @Mapping(target = "logLimit", source = "deliveryDomain.limit")
    @Mapping(target = "apiPath", source = "deliveryDomain.path")
    @Mapping(target = "apiBaseUrl", source = "deliveryDomain.endpoint")
    @Mapping(target = "accessKey", source = "deliveryDomain.ak")
    DeliveryLogRecoveryConfigDTO getDeliveryLogRecoveryConfigDTO(LogDeliveryDomainDTO deliveryDomain, String bizType, String targetType, Integer batchLimit);

    @Mapping(target = "logLimit", source = "paramDTO.limit")
    JdLiveContext getJdLiveLogContext(LogDeliveryParamDTO paramDTO, LogDeliveryTaskDTO deliveryTask, Integer retryCount);

    @Mapping(target = "logDetails", expression = "java(new java.util.ArrayList<>())")
    @Mapping(target = "curRetryCount", ignore = true)
    AliCdnRecoveryContext getAliCdnRecoveryContext(DeliveryLogRecoveryConfigDTO configDTO);

    @Mapping(target = "logDetails", expression = "java(new java.util.ArrayList<>())")
    @Mapping(target = "curRetryCount", ignore = true)
    JdLiveRecoveryContext getJdLiveRecoveryContext(DeliveryLogRecoveryConfigDTO configDTO);
}
