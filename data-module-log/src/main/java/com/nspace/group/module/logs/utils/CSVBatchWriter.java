package com.nspace.group.module.logs.utils;

import cn.hutool.core.io.FileUtil;
import com.nspace.group.module.logs.service.offlinelog.dto.FileMeta;
import com.opencsv.CSVWriter;
import com.opencsv.CSVWriterBuilder;
import com.opencsv.ICSVWriter;
import lombok.Getter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.compressors.gzip.GzipCompressorOutputStream;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;

import java.io.*;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;

@Slf4j
@Getter
public class CSVBatchWriter implements Closeable {
    private final String filePrefix;
    private final int batchSize;
    private int fileIndex = 0;
    private int rowCount = 0;
    private ICSVWriter csvWriter;
    private String currentFileName;
    private final List<FileMeta> metas = new CopyOnWriteArrayList<>();
    private final List<CompletableFuture<Void>> futures = new ArrayList<>();
    private long currentMills;

    public CSVBatchWriter(String filePrefix, int batchSize) throws IOException {
        this.filePrefix = filePrefix;
        this.batchSize = batchSize;
        this.currentMills = System.currentTimeMillis();
        openNewWriter();
    }

    private void openNewWriter() throws IOException {
        closeWriter();
        currentFileName = filePrefix + "-" + (fileIndex++) + ".csv";
        FileUtil.newFile(currentFileName);
        csvWriter = new CSVWriterBuilder(new FileWriter(currentFileName))
                .withSeparator(',')  // 设置分隔符（默认是逗号）
                .withQuoteChar(CSVWriter.NO_QUOTE_CHARACTER)  // 禁用引号
                .build();
        rowCount = 0;
    }


    @SneakyThrows
    public void writeRow(String[] row) {
        if (rowCount >= batchSize) {
            openNewWriter();
        }
        csvWriter.writeNext(row);
        rowCount++;
    }

    @SneakyThrows
    private void compressGzip(FileMeta meta) {
        long startTime = System.currentTimeMillis();
        String fileName = meta.getLogName();
        String gzipFileName = fileName.replace(".csv", ".gz");
        try (FileInputStream fis = new FileInputStream(fileName);
             BufferedReader reader = new BufferedReader(new InputStreamReader(fis));
             GzipCompressorOutputStream gos = new GzipCompressorOutputStream(new FileOutputStream(gzipFileName));
             BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(gos))) {
            IOUtils.copyLarge(reader, writer);
            FileUtils.deleteQuietly(new File(fileName));
            log.debug("Compress file {} in {} ms", gzipFileName, System.currentTimeMillis() - startTime);
            meta.setFileSize(FileUtil.size(new File(gzipFileName)));
            meta.setLogName(gzipFileName);
        }
    }

    public CompletableFuture<List<FileMeta>> getFiles() throws IOException {
        closeWriter();
        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenApply((unused) -> metas);
    }

    private void closeWriter() throws IOException {
        if (csvWriter != null) {
            csvWriter.flush();
            csvWriter.close();
            log.debug("completed write {} in {} ms", currentFileName, System.currentTimeMillis() - currentMills);
            currentMills = System.currentTimeMillis();
            if (currentFileName != null) {
                FileMeta meta = FileMeta.builder()
                        .logName(currentFileName).rows(rowCount).build();
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    this.compressGzip(meta);
                    metas.add(meta);
                }, Constant.COMPRESSOR_EXECUTORS);
                futures.add(future);
            }
        }
    }

    @Override
    public void close() throws IOException {

    }
}
