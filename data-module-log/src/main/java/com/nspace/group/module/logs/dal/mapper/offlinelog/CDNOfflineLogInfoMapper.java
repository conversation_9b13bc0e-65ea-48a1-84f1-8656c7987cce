package com.nspace.group.module.logs.dal.mapper.offlinelog;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.nspace.group.framework.mybatis.core.mapper.BaseMapperX;
import com.nspace.group.module.logs.dal.dataobject.offlinelog.CDNOfflineLogInfoDO;
import com.nspace.group.module.logs.dal.dataobject.offlinelog.DelayedOfflineLogDO;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
@DS("nspace_log")
public interface CDNOfflineLogInfoMapper extends BaseMapperX<CDNOfflineLogInfoDO> {

    List<DelayedOfflineLogDO> getHistoryLogs(String batchNum);
}
