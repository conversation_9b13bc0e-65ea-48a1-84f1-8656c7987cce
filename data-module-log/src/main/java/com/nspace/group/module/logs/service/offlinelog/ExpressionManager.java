package com.nspace.group.module.logs.service.offlinelog;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Expression;
import com.googlecode.aviator.FunctionLoader;
import com.googlecode.aviator.runtime.type.AviatorFunction;
import com.nspace.group.module.logs.dal.dataobject.offlinelog.OfflineLogExpressionDO;
import com.nspace.group.module.logs.dal.mapper.offlinelog.OfflineLogExpressionMapper;
import com.nspace.group.module.logs.utils.CacheComponent;
import org.apache.catalina.core.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class ExpressionManager {


    @Resource
    private OfflineLogExpressionMapper expressionMapper;


    private static final Map<String, Expression> expressionsMap = new ConcurrentHashMap<>();

    public Map<Integer, Expression> getExpressions(Long tenantId, String domain, String logType) {
        Wrapper<OfflineLogExpressionDO> wrapper = Wrappers.<OfflineLogExpressionDO>lambdaQuery()
                .eq(OfflineLogExpressionDO::getTenantId, tenantId)
                .eq(OfflineLogExpressionDO::getDomain, domain)
                .eq(OfflineLogExpressionDO::getLogType, logType);
        List<OfflineLogExpressionDO> expressionDOS = expressionMapper.selectList(wrapper);
        Map<Integer, Expression> expressions = new HashMap<>();
        expressionDOS.forEach(expressionDO -> {
            String expression = expressionDO.getExpression();
            Expression compiledExpression;
            if (!expressionsMap.containsKey(expression)) {
                compiledExpression = AviatorEvaluator.compile(expression);
                expressionsMap.put(expression, compiledExpression);
            } else {
                compiledExpression = expressionsMap.get(expression);
            }
            expressions.put(expressionDO.getFieldIndex(), compiledExpression);
        });
        return expressions;
    }

}
