package com.nspace.group.module.logs.dal.dataobject.offlinelog;

import com.nspace.group.module.logs.enums.ExportTypeEnum;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;

@Data
public class OfflineLogDO {

    /**
     * 批次
     */
    private String batchNum;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 域名
     */
    private String domain;

    /**
     * 日志类型
     */
    private String logType;

    /**
     * 导出类型
     */
    private ExportTypeEnum exportType;

    private Integer lifeCycle = 30;


    /**
     * 当前任务的开始时间
     */
    private LocalDateTime startTimestamp;

    private Integer totalBatch;

    private Integer executeBatch;

    private Integer retryCount;


    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        OfflineLogDO that = (OfflineLogDO) o;
        return Objects.equals(batchNum, that.batchNum) && Objects.equals(logType, that.logType)
                && Objects.equals(exportType, that.exportType)
                && Objects.equals(tenantId, that.tenantId) && Objects.equals(domain, that.domain)
                && Objects.equals(retryCount, that.retryCount);
    }

    @Override
    public int hashCode() {
        return Objects.hash(batchNum, logType, exportType, tenantId, domain, retryCount);
    }
}
