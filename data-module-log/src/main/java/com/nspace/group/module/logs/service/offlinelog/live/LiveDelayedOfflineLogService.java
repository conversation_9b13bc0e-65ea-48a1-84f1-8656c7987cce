package com.nspace.group.module.logs.service.offlinelog.live;

import cn.hutool.core.collection.CollUtil;
import com.nspace.group.module.logs.dal.dataobject.offlinelog.DelayedOfflineLogDO;
import com.nspace.group.module.logs.dal.dataobject.offlinelog.OfflineLogDO;
import com.nspace.group.module.logs.dal.mapper.offlinelog.CDNOfflineLogMapper;
import com.nspace.group.module.logs.dal.mapper.offlinelog.LiveOfflineLogInfoMapper;
import com.nspace.group.module.logs.dal.mapper.offlinelog.ODSOfflineLogMapper;
import com.nspace.group.module.logs.service.offlinelog.DelayedOfflineLogService;
import com.nspace.group.module.logs.service.offlinelog.dto.OfflineLogDTO;
import com.nspace.group.module.logs.utils.DTF;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service("live-offline-delayed-fetcher")
public class LiveDelayedOfflineLogService implements DelayedOfflineLogService {

    @Resource
    private LiveOfflineLogInfoMapper liveOfflineLogMapper;
    @Resource
    private CDNOfflineLogMapper offlineLogMapper;
    @Resource
    private ODSOfflineLogMapper odsLogMapper;


    @Override
    public List<OfflineLogDTO> getDelayedLogs(String batchNum) {
        List<DelayedOfflineLogDO> delayedLogs = liveOfflineLogMapper.getHistoryLogs(batchNum);
        Map<String, DelayedOfflineLogDO> syncedMap = delayedLogs.stream()
                .collect(Collectors.toMap(DelayedOfflineLogDO::getDomain, Function.identity()));
        List<OfflineLogDO> dos = offlineLogMapper.selectByCurrent();
        dos.stream().filter(item -> "cdn".equals(item.getLogType()))
                .forEach(item -> {
                    if (!syncedMap.containsKey(item.getDomain())) {
                        DelayedOfflineLogDO delayedLogDO = new DelayedOfflineLogDO();
                        delayedLogDO.setDomain(item.getDomain());
                        delayedLogDO.setTimestamp(null);
                        delayedLogDO.setSyncedCount(0);
                        delayedLogDO.setLifeCycle(item.getLifeCycle());
                        delayedLogs.add(delayedLogDO);
                        syncedMap.put(item.getDomain(), delayedLogDO);
                    }
                });
        if (CollUtil.isEmpty(delayedLogs)) {
            return Collections.emptyList();
        }
        LocalDateTime dateTime = LocalDateTime.parse(batchNum, DTF.yyyyMMddHH);
        LocalDateTime startTime = dateTime.withMinute(0).withSecond(0);
        LocalDateTime endTime = dateTime.withMinute(59).withSecond(59);
        List<DelayedOfflineLogDO> odsDelayedLogs = odsLogMapper.selectLiveDelayedLogs(startTime, endTime, delayedLogs);
        return odsDelayedLogs.stream().map(item -> {
            OfflineLogDTO dto = new OfflineLogDTO();
            DelayedOfflineLogDO syncDO = syncedMap.get(item.getDomain());
            dto.setBatchNum(item.getBatchNum());
            dto.setDomain(item.getDomain());
            dto.setTimestamp(item.getTimestamp());
            if (!Objects.equals(syncDO.getSyncedCount(), item.getSyncedCount())) {
                dto.setSyncAll(Boolean.TRUE);
            } else if (item.getUnsyncedCount() > 0) {
                dto.setRefresh(Boolean.TRUE);
            }
            return dto;
        }).collect(Collectors.toList());
    }
}
