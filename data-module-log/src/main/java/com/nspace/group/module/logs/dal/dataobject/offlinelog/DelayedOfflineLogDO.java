package com.nspace.group.module.logs.dal.dataobject.offlinelog;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class DelayedOfflineLogDO {

    private String batchNum;

    private String logType;

    private Long tenantId;

    private String domain;

    private Integer syncedCount;

    private Integer unsyncedCount;

    private LocalDateTime timestamp;

    private Integer lifeCycle;
}
