package com.nspace.group.module.logs.convert.delivery;

import com.aliyun.openservices.log.common.LogItem;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.mapstruct.Mapper;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version :GeneralCdnLogConvert.java, v0.1 2025年03月19日 10:21 zhangxin Exp
 */
@Mapper
public interface GeneralCdnLogConvert {

    GeneralCdnLogConvert INSTANCE = Mappers.getMapper(GeneralCdnLogConvert.class);
    JsonMapper jsonMapper = JsonMapper.builder()
            .addModule(new JavaTimeModule())
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
            .enable(JsonGenerator.Feature.WRITE_BIGDECIMAL_AS_PLAIN)
            .propertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE)
            .build();

    @Named("strToLogItemsConvert")
    default List<LogItem> getCdnAliLogItemsFromJson(String logJsonArr) throws JsonProcessingException {
        JsonNode jsonNode = jsonMapper.readTree(logJsonArr);
        List<LogItem> logItemList = new ArrayList<>();
        for (JsonNode node : jsonNode) {
            LogItem logItem = new LogItem();
            for (JsonNode mContent : node.path("mContents")) {
                if (mContent.path("mKey").asText().equals("unixtime")) {
                    logItem.SetTime(Integer.parseInt(mContent.path("mValue").asText()));
                }
                logItem.PushBack(mContent.path("mKey").asText(), mContent.path("mValue").asText());
            }
            logItemList.add(logItem);
        }
        return logItemList;
    }

}
