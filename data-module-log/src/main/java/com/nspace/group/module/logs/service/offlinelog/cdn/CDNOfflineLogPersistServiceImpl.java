package com.nspace.group.module.logs.service.offlinelog.cdn;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.nspace.group.module.logs.dal.dataobject.offlinelog.CDNOfflineLogDetailInfoDO;
import com.nspace.group.module.logs.dal.dataobject.offlinelog.CDNOfflineLogInfoDO;
import com.nspace.group.module.logs.dal.dataobject.offlinelog.OfflineLogDetailInfoDO;
import com.nspace.group.module.logs.dal.dataobject.offlinelog.OfflineLogInfoDO;
import com.nspace.group.module.logs.dal.mapper.offlinelog.CDNOfflineLogDetailInfoMapper;
import com.nspace.group.module.logs.dal.mapper.offlinelog.CDNOfflineLogInfoMapper;
import com.nspace.group.module.logs.enums.OfflineStatusEnum;
import com.nspace.group.module.logs.service.offlinelog.OfflinePersistService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

@Service("cdn-offline-persist")
public class CDNOfflineLogPersistServiceImpl implements OfflinePersistService {

    @Resource
    private CDNOfflineLogInfoMapper cdnOfflineLogMapper;

    @Resource
    private CDNOfflineLogDetailInfoMapper detailInfoMapper;


    @Override
    public void persist(OfflineLogInfoDO logDO) {
        if (logDO instanceof OfflineLogDetailInfoDO) {
            CDNOfflineLogDetailInfoDO detailDO = new CDNOfflineLogDetailInfoDO();
            BeanUtils.copyProperties(logDO, detailDO);
            if (logDO.getId() == null) {
                detailInfoMapper.insert(detailDO);
            } else {
                detailInfoMapper.updateById(detailDO);
            }
            logDO.setId(detailDO.getId());
        } else if (logDO != null) {
            CDNOfflineLogInfoDO infoDO = new CDNOfflineLogInfoDO();
            BeanUtils.copyProperties(logDO, infoDO);
            if (logDO.getId() == null) {
                cdnOfflineLogMapper.insert(infoDO);
            } else {
                cdnOfflineLogMapper.updateById(infoDO);
            }
            logDO.setId(infoDO.getId());
        }

    }

    @Override
    public void batchUpdate(List<Long> ids, String message, OfflineStatusEnum status, List<Long> detailIds) {
        LambdaUpdateWrapper<CDNOfflineLogInfoDO> wrapper = new UpdateWrapper<CDNOfflineLogInfoDO>().lambda()
                .set(CDNOfflineLogInfoDO::getStatus, status)
                .set(CDNOfflineLogInfoDO::getMessage, message)
                .in(CDNOfflineLogInfoDO::getId, ids);
        cdnOfflineLogMapper.update(wrapper);
        if (CollectionUtil.isNotEmpty(detailIds)) {
            detailInfoMapper.deleteBatchIds(detailIds);
        }
    }

    @Override
    public void batchUpdateDetails(List<Long> ids, String message, OfflineStatusEnum status) {
        LambdaUpdateWrapper<CDNOfflineLogDetailInfoDO> wrapper = new UpdateWrapper<CDNOfflineLogDetailInfoDO>().lambda()
                .set(CDNOfflineLogDetailInfoDO::getStatus, status)
                .set(CDNOfflineLogDetailInfoDO::getMessage, message)
                .in(CDNOfflineLogDetailInfoDO::getId, ids);
        detailInfoMapper.update(wrapper);
    }


    @Override
    public void remove(String batchNum, Long tenantId, String domain, Integer executeBatch) {

        LambdaQueryWrapper<CDNOfflineLogInfoDO> wrapper = Wrappers.lambdaQuery(CDNOfflineLogInfoDO.class)
                .eq(CDNOfflineLogInfoDO::getBatchNum, batchNum)
                .eq(CDNOfflineLogInfoDO::getTenantId, tenantId)
                .eq(CDNOfflineLogInfoDO::getDomain, domain);
        cdnOfflineLogMapper.delete(wrapper);

        LambdaQueryWrapper<CDNOfflineLogDetailInfoDO> detailWrapper = Wrappers.lambdaQuery(CDNOfflineLogDetailInfoDO.class)
                .eq(CDNOfflineLogDetailInfoDO::getBatchNum, batchNum)
                .eq(CDNOfflineLogDetailInfoDO::getTenantId, tenantId)
                .eq(CDNOfflineLogDetailInfoDO::getDomain, domain)
                .eq(executeBatch != null, CDNOfflineLogDetailInfoDO::getExecuteBatch, executeBatch);
        detailInfoMapper.delete(detailWrapper);
    }

    @Override
    public List<? extends OfflineLogDetailInfoDO> find(String batchNum, Long tenantId, String domain) {
        LambdaQueryWrapper<CDNOfflineLogDetailInfoDO> wrapper = Wrappers.lambdaQuery(CDNOfflineLogDetailInfoDO.class)
                .eq(CDNOfflineLogDetailInfoDO::getBatchNum, batchNum)
                .eq(CDNOfflineLogDetailInfoDO::getTenantId, tenantId)
                .eq(CDNOfflineLogDetailInfoDO::getDomain, domain);
        return detailInfoMapper.selectList(wrapper);
    }

    @Override
    public int cleanExpiredLogs() {
        LocalDateTime startOfDay = LocalDateTime.now().with(LocalTime.MIN);
        QueryWrapper<CDNOfflineLogInfoDO> wrapper = Wrappers.query(CDNOfflineLogInfoDO.class)
                .isNotNull("lifecycle")
                .apply("create_time + CAST(lifecycle || ' days' AS interval) < {0}", startOfDay);
        return cdnOfflineLogMapper.delete(wrapper);
    }


}
