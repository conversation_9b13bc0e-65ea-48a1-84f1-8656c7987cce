package com.nspace.group.module.logs.service.offlinelog;

import com.nspace.group.module.logs.dal.dataobject.offlinelog.OfflineLogDetailInfoDO;
import com.nspace.group.module.logs.dal.dataobject.offlinelog.OfflineLogInfoDO;
import com.nspace.group.module.logs.enums.OfflineStatusEnum;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface OfflinePersistService {
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    void persist(OfflineLogInfoDO logDO);


    /**
     * @param ids       日志列表 的 is
     * @param message   错误信息
     * @param status    状态
     * @param detailIds 详情id 列表
     */
    void batchUpdate(List<Long> ids, String message, OfflineStatusEnum status, List<Long> detailIds);

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    void batchUpdateDetails(List<Long> ids, String message, OfflineStatusEnum status);

    void remove(String batchNum, Long tenantId, String domain, Integer executeBatch);

    List<? extends OfflineLogDetailInfoDO> find(String batchNum, Long tenantId, String domain);

    int cleanExpiredLogs();
}
