package com.nspace.group.module.logs.biz.delivery.strategy.cdn;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.aliyun.openservices.aliyun.log.producer.Producer;
import com.aliyun.openservices.aliyun.log.producer.Result;
import com.aliyun.openservices.log.common.LogItem;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.MoreExecutors;
import com.nspace.group.framework.common.enums.BusinessTypeEnum;
import com.nspace.group.framework.common.util.json.JsonUtils;
import com.nspace.group.module.logs.biz.delivery.DeliveryLogDetailService;
import com.nspace.group.module.logs.biz.delivery.context.AliCdnRecoveryContext;
import com.nspace.group.module.infra.design.context.LogContext;
import com.nspace.group.module.infra.design.strategy.LogSendStrategy;
import com.nspace.group.module.logs.convert.delivery.GeneralCdnLogConvert;
import com.nspace.group.module.logs.enums.LogDeliveryStatusEnum;
import com.nspace.group.module.logs.service.delivery.dto.DeliveryLogDetailDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Component("aliCdnRecoveryLogSendStrategy")
public class AliRecoveryLogSendStrategy implements LogSendStrategy {

    @Resource
    private Producer logProducer;

    @Resource
    private DeliveryLogDetailService deliveryLogDetailService;

    @Override
    public boolean send(LogContext logContext) {
        AliCdnRecoveryContext context = (AliCdnRecoveryContext) logContext;
        String targetType = context.getTargetType();
        Integer logLimit = context.getLogLimit();
        List<DeliveryLogDetailDTO> logDetails = context.getLogDetails();

        if (logDetails.isEmpty()) {
            log.info("no_logs_to_recover,target_type={}", targetType);
            return true;
        }
        logDetails.forEach(logDetail -> logDetail.setDeliveryTimes(context.getCurRetryCount()));
        List<ListenableFuture<Result>> futures = new ArrayList<>();
        Lists.partition(logDetails, logLimit).forEach(subLogDetails -> {
            ListenableFuture<Result> f = send(context, subLogDetails);
            futures.add(f);
        });
        // 阻塞当前线程直至所有投递任务执行结束或异常退出
        Set<Boolean> sendResults = new HashSet<>();
        for (ListenableFuture<Result> f : futures) {
            try {
                sendResults.add(f.get().isSuccessful());
            } catch (Exception e) {
                log.error("send,unknown_exception,error={}", ExceptionUtil.getRootCauseMessage(e));
                sendResults.add(false);
            }
        }
        return !sendResults.contains(false);
    }

    private ListenableFuture<Result> send(AliCdnRecoveryContext context, List<DeliveryLogDetailDTO> logDetails) {

        String project = context.getProject();
        String logStore = context.getLogStore();
        Integer curRetryCount = context.getCurRetryCount();

        try {
            String jsonArrStr = logDetails.stream()
                    .map(DeliveryLogDetailDTO::getLogJson)
                    .collect(Collectors.joining(StringPool.COMMA, StringPool.LEFT_SQ_BRACKET, StringPool.RIGHT_SQ_BRACKET));
            List<LogItem> logItems = GeneralCdnLogConvert.INSTANCE.getCdnAliLogItemsFromJson(jsonArrStr);
            log.info("try[{}],deliver_log,project={},logStore={},logItems={}",
                    curRetryCount, project, logStore, JsonUtils.toJsonString(logItems));
            ListenableFuture<Result> f = logProducer.send(project, logStore, logItems);
            Futures.addCallback(f, new LogSendFutureCallback(curRetryCount, project, logStore, logDetails), MoreExecutors.directExecutor());
            return f;
        } catch (Exception e) {
            throw ExceptionUtil.wrapRuntime(ExceptionUtil.getRootCause(e));
        }
    }

    private final class LogSendFutureCallback implements FutureCallback<Result> {

        private final Integer curRetryCount;

        private final String project;

        private final String logStore;

        private final List<DeliveryLogDetailDTO> logDetails;

        LogSendFutureCallback(Integer curRetryCount, String project, String logStore, List<DeliveryLogDetailDTO> logDetails) {
            this.curRetryCount = curRetryCount;
            this.project = project;
            this.logStore = logStore;
            this.logDetails = logDetails;
        }

        @Override
        public void onSuccess(@Nullable Result result) {
            log.info("try[{}],log_delivered,project={},logStore={},result={}",
                    curRetryCount, project, logStore, JsonUtils.toJsonString(result));
            logDetails.forEach(deliveryDetail -> {
                deliveryDetail.setLogStatus(LogDeliveryStatusEnum.SUCCESS.getStatus());
                deliveryDetail.setDeliveryTimestamp(LocalDateTime.now());
            });
            deliveryLogDetailService.updateMany(BusinessTypeEnum.BUSINESS_TYPE_CDN.getCode(), logDetails);
        }

        @Override
        public void onFailure(Throwable t) {
            log.error("try[{}],log_delivery_failure,project={},logStore={},error={}",
                    curRetryCount, project, logStore, ExceptionUtil.getRootCauseMessage(t));
        }
    }
}
