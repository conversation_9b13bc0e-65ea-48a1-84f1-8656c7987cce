package com.nspace.group.module.logs.service.offlinelog.live;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.nspace.group.module.logs.service.offlinelog.OfflineLogFetcherService;
import com.nspace.group.module.logs.utils.Constant;
import com.nspace.group.module.logs.utils.DTF;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.time.LocalDateTime;

@Slf4j
@DS("arrow_doris")
@Service("live-offline-fetcher")
public class LiveOfflineLogFetcherServiceImpl implements OfflineLogFetcherService {

    @Resource
    private DataSource dataSource;

    private static final String LIVE_SQL = "select %s from nspace_analysis.tb_ods_billing_log_stream_pull " +
            "where log_time between '%s' and '%s' and internal = 0 and domain = '%s'";

    @Override
    public ResultSet fetchLogsResult(LocalDateTime startTime, LocalDateTime endTime, String domain
            , LocalDateTime timeStamp, Integer fetchSize) throws SQLException {
        Connection connection = dataSource.getConnection();
        Statement statement = connection.createStatement();
        statement.setFetchSize(fetchSize);
        String executeSQL = String.format(LIVE_SQL, Constant.LIVE_SELECT_FIELDS
                , startTime.format(DTF.yyyyMMddHHmmss), endTime.format(DTF.yyyyMMddHHmmss), domain);
        if (timeStamp != null) {
            executeSQL = String.format("%s and cur_timestamp  > %s", executeSQL, timeStamp);
        }
        log.debug("executeSQL: {}", executeSQL);
        return statement.executeQuery(executeSQL);
    }
}
