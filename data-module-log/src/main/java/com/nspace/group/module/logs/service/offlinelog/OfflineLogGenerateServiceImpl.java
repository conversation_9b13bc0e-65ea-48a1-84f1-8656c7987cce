package com.nspace.group.module.logs.service.offlinelog;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import com.googlecode.aviator.Expression;
import com.nspace.group.framework.file.core.FileClient;
import com.nspace.group.framework.file.core.dto.MetaDTO;
import com.nspace.group.module.logs.config.logs.OfflineLogConfiguration;
import com.nspace.group.module.logs.dal.dataobject.offlinelog.OfflineLogDetailInfoDO;
import com.nspace.group.module.logs.dal.dataobject.offlinelog.OfflineLogInfoDO;
import com.nspace.group.module.logs.dal.mapper.offlinelog.OfflineLogMapper;
import com.nspace.group.module.logs.enums.MergeTypeEnum;
import com.nspace.group.module.logs.enums.OfflineStatusEnum;
import com.nspace.group.module.logs.service.offlinelog.dto.FileMeta;
import com.nspace.group.module.logs.service.offlinelog.dto.OfflineLogDTO;
import com.nspace.group.module.logs.utils.*;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.sql.ResultSet;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @version :OfflineLogGenerateServiceImpl.java, v0.1 2024年12月18日 11:52 Exp
 */
@Service
@Slf4j
public class OfflineLogGenerateServiceImpl implements OfflineLogGenerateService {


    @Resource
    private OfflineLogFactory logFactory;
    @Resource
    private FileClient fileClient;
    @Resource
    private ExpressionManager expressionManager;
    @Resource
    private OfflineLogConfiguration configuration;


    @Override
    public void generateOfflineLog(OfflineLogDTO dto, boolean removeOldLog) {
        String logType = dto.getLogType();
        OfflinePersistService persistService = logFactory.getPersistService(logType);

        //删除旧的导出历史
        String batchNum = dto.getBatchNum();
        String domain = dto.getDomain();
        Integer executeBatch = dto.getExecuteBatch();
        if (removeOldLog) {
            persistService.remove(batchNum, dto.getTenantId(), domain, executeBatch);
        }

        OfflineLogMapper offlineLogMapper = logFactory.getOfflineLogMapper(logType);


        LocalDateTime dateTime = LocalDateTime.parse(batchNum, DTF.yyyyMMddHH);
        LocalDateTime start = dateTime.withMinute(0).withSecond(0);
        LocalDateTime end = dateTime.withMinute(59).withSecond(59);
        Long totalCount = offlineLogMapper.getCurrentBatchCount(start, end, domain);

        if (totalCount == 0) {
            return;
        }

        //如果数据库数据过少,则只进行一次分批处理
        Integer totalBatch = Optional.ofNullable(dto.getTotalBatch())
                .orElse(configuration.getTotalBatch());

        totalBatch = Math.toIntExact(Math.max(1, Math.min(totalCount / (configuration.getBatchSize()), totalBatch)));
        dto.setTotalBatch(totalBatch);
        int minute = 60 / totalBatch;

        List<CompletableFuture<Void>> futures;
        if (executeBatch != null) {
            // 单个批次
            futures = List.of(executeQuietly(OfflineLogDTO.newDTO(dto), executeBatch, minute));
        } else {
            // 全部子批次
            futures = new ArrayList<>();
            for (int batch = 0; batch < totalBatch; batch++) {
                futures.add(executeQuietly(OfflineLogDTO.newDTO(dto), batch, minute));
            }
        }
        CompletableFuture<Void> future;
        if (dto.getTotalBatch() > 1) {
            future = CompletableFuture.allOf(wrap(futures))
                    .whenCompleteAsync((v, e) -> {
                        mergeSubBatch(OfflineLogDTO.newDTO(dto));
                    }, Constant.IO_EXECUTORS);
        } else {
            future = CompletableFuture.completedFuture(null);
        }
        future.whenComplete((v, e) -> {
            if (e != null) {
                OfflineLogInfoDO infoDO = OfflineConverters.convert(dto);
                infoDO.setMessage(e.getMessage());
                infoDO.setStatus(OfflineStatusEnum.FAILED);
                persistService.persist(infoDO);
                log.error(e.getMessage(), e);
            }
        });
    }

    private CompletableFuture<Void> executeQuietly(OfflineLogDTO dto, Integer batch, int minute) {
        OfflinePersistService persistService = logFactory.getPersistService(dto.getLogType());
        return execute(dto, batch, minute)
                .whenComplete((v, e) -> {
                    if (e != null) {
                        log.error("Batch {} failed: {}", batch, e.getMessage(), e);
                        OfflineLogInfoDO infoDO = OfflineConverters.convert(dto);
                        infoDO.setMessage(e.getMessage());
                        infoDO.setStatus(OfflineStatusEnum.FAILED);
                        persistService.persist(infoDO);
                    }
                });
    }

    /**
     * 合并子批次
     *
     * @param dto 实体
     */
    @Override
    public void mergeSubBatch(OfflineLogDTO dto) {
        log.debug("合并所有子批次 {}", dto.getBatchNum());
        OfflinePersistService persistService = logFactory.getPersistService(dto.getLogType());
        List<? extends OfflineLogDetailInfoDO> dos = persistService
                .find(dto.getBatchNum(), dto.getTenantId(), dto.getDomain());
        Map<Integer, List<OfflineLogDetailInfoDO>> batchMapping = dos.stream()
                .filter(item -> item.getTotalBatch() != null)
                .collect(Collectors.groupingBy(OfflineLogDetailInfoDO::getTotalBatch));

        batchMapping.forEach((key, value) -> {
            Map<Integer, List<OfflineLogDetailInfoDO>> executeBatches = value.stream()
                    .filter(item -> item.getExecuteBatch() != null)
                    .collect(Collectors.groupingBy(OfflineLogDetailInfoDO::getExecuteBatch));
            List<OfflineLogInfoDO> mergeDOs = new ArrayList<>();
            boolean canMerge = IntStream.range(0, key)
                    .allMatch(i -> {
                        boolean success = isSuccess(executeBatches, i);
                        if (success) {
                            mergeDOs.addAll(executeBatches.get(i));
                        }
                        return success;
                    });
            if (canMerge) {
                mergeSubBatchFiles(dto, mergeDOs);
            }
        });
    }

    private boolean isSuccess(Map<Integer, List<OfflineLogDetailInfoDO>> executeBatches, int batch) {
        List<OfflineLogDetailInfoDO> executeBatch = executeBatches.get(batch);
        if (CollUtil.isEmpty(executeBatch)) {
            log.info("子批次 {} 缺失", batch);
            return false;
        }
        boolean allPartialSuccess = executeBatch.stream()
                .allMatch(item -> item.getStatus() == OfflineStatusEnum.SUCCESS);
        if (allPartialSuccess) {
            return true;
        } else {
            log.info("子批次 {} 未成功", batch);
            return false;
        }
    }

    /**
     * 合并所有子批次的文件
     *
     * @param dto      离线日志实体
     * @param mergeDOS 待合并的子批次
     */
    private void mergeSubBatchFiles(OfflineLogDTO dto, List<? extends OfflineLogInfoDO> mergeDOS) {
        OfflinePersistService persistService = logFactory.getPersistService(dto.getLogType());
        List<Long> ids = mergeDOS.stream()
                .map(OfflineLogInfoDO::getId).collect(Collectors.toList());
        //取所有子批次的最大时间作为任务批次的最大时间
        LocalDateTime maxTimeStamp = mergeDOS.stream()
                .map(OfflineLogInfoDO::getMaxTimestamp)
                .filter(Objects::nonNull)
                .max(LocalDateTime::compareTo)
                .orElse(null);
        List<FileMeta> metas = mergeDOS.stream()
                .map(item -> {
                    FileMeta fileMeta = new FileMeta();
                    fileMeta.setFileSize(item.getFileSize());
                    fileMeta.setLogName(item.getLogName());
                    fileMeta.setRows(item.getRows());
                    return fileMeta;
                }).collect(Collectors.toList());
        String fileName = String.format("%s_%s_%%02d.gz", dto.getDomain(), dto.getBatchNum());
        List<String> objectKeys = metas.stream()
                .map(FileMeta::getLogName).collect(Collectors.toList());
        MergeResponse response = mergeAndPersist(dto, fileName, metas, maxTimeStamp, MergeTypeEnum.HYBRID_MERGE);
        CompletableFuture.allOf(wrap(response.futures))
                .whenComplete((result, ex) -> {
                    if (CollUtil.isNotEmpty(response.getIds())) {
                        if (ex == null) {
                            persistService.batchUpdate(response.getIds(), null, OfflineStatusEnum.SUCCESS, ids);
                            objectKeys.forEach(objectKey -> {
                                fileClient.delete(dto.getLogType(), objectKey);
                            });
                        } else {
                            log.error(ex.getMessage(), ex);
                            persistService.batchUpdate(response.getIds(), ex.getMessage(), OfflineStatusEnum.FAILED
                                    , Collections.emptyList());
                        }
                    }
                    response.getFiles().forEach(item -> {
                        FileUtils.deleteQuietly(new File(item));
                    });
                });
    }


    private CompletableFuture<Void> execute(OfflineLogDTO dto, int executeBatch, int minute) {
        String logType = dto.getLogType();
        String domain = dto.getDomain();
        String batchNum = dto.getBatchNum();
        dto.setExecuteBatch(executeBatch);

        OfflineLogFetcherService fetcherService = logFactory.getFetcherService(logType);
        OfflineLogTransformerService transformerService = logFactory.getTransformerService(logType);

        LocalDateTime dateTime = LocalDateTime.parse(batchNum, DTF.yyyyMMddHH);

        ResultSet resultSet = null;
        String filePrefix = UUID.randomUUID().toString();
        long maxIngestTimestamp = Long.MIN_VALUE;
        String csvPrefix = configuration.getPath() + "/" + filePrefix;
        try (CSVBatchWriter csvBatchWriter = new CSVBatchWriter(csvPrefix, configuration.getCsvRows())) {
            LocalDateTime startTime = dateTime.withMinute(executeBatch * minute).withSecond(0);
            LocalDateTime endTime = dateTime.withMinute((executeBatch + 1) * minute - 1).withSecond(59);
            Map<Integer, Expression> defaultExpressions = transformerService.getDefaultExpressions();
            Map<Integer, Expression> expressions = expressionManager.getExpressions(dto.getTenantId(), domain, logType);
            defaultExpressions.putAll(expressions);
            resultSet = fetcherService.fetchLogsResult(startTime, endTime, domain
                    , dto.getTimestamp(), configuration.getFetchSize());
            while (resultSet.next()) {
                long curTimestamp = resultSet.getTimestamp("cur_timestamp").getTime();
                maxIngestTimestamp = Math.max(maxIngestTimestamp, curTimestamp);
                String[] row = transformerService.transform(defaultExpressions, resultSet);
                csvBatchWriter.writeRow(row);
            }
            //fix: 临时修复时区不对的问题
            LocalDateTime maxTimestamp = maxIngestTimestamp > 0 ? Instant.ofEpochMilli(maxIngestTimestamp)
                    .atZone(ZoneId.systemDefault()).plusHours(configuration.getOffset()).toLocalDateTime() : null;

            CompletableFuture<List<FileMeta>> future = csvBatchWriter.getFiles();
            String fileName = String.format("%s_%%02d.gz", filePrefix);
            if (dto.getTotalBatch() == 1) {
                fileName = String.format("%s_%s_%%02d.gz", dto.getDomain(), dto.getBatchNum());
            }
            String finalName = fileName;
            return future.thenApplyAsync(metas -> {
                        dto.setExecuteBatch(executeBatch);
                        return mergeAndPersist(dto, finalName, metas, maxTimestamp, MergeTypeEnum.LOCAL);
                    }, Constant.IO_EXECUTORS)
                    .thenCompose(response -> CompletableFuture.allOf(wrap(response.futures))
                            .whenComplete((result, ex) -> {
                                List<Long> ids = response.getIds();
                                persistLogRecords(dto, ex, ids);
                                response.getFiles().forEach(item -> {
                                    FileUtils.deleteQuietly(new File(item));
                                });
                            }));
        } catch (Exception e) {
            return CompletableFuture.failedFuture(e);
        } finally {
            DbUtils.close(resultSet);
        }
    }

    //如果 totalBatch 是 1, 则不会进行子批次的合并,而是直接保存为完整记录
    private void persistLogRecords(OfflineLogDTO dto, Throwable ex, List<Long> ids) {
        OfflinePersistService persistService = logFactory.getPersistService(dto.getLogType());
        if (dto.getTotalBatch() == 1) {
            if (ex != null) {
                log.error(ex.getMessage());
                persistService.batchUpdate(ids, ex.getMessage(), OfflineStatusEnum.FAILED
                        , Collections.emptyList());
            } else {
                persistService.batchUpdate(ids, null, OfflineStatusEnum.SUCCESS
                        , Collections.emptyList());
            }
        } else {
            if (ex != null) {
                log.error(ex.getMessage());
                persistService.batchUpdateDetails(ids, ex.getMessage(), OfflineStatusEnum.FAILED);
            } else {
                persistService.batchUpdateDetails(ids, null, OfflineStatusEnum.SUCCESS);
            }
        }
    }


    private MergeResponse mergeAndPersist(OfflineLogDTO dto, String fileName, List<FileMeta> metas
            , LocalDateTime maxTimeStamp, MergeTypeEnum mergeType) {
        String logType = dto.getLogType();
        OfflinePersistService persistService = logFactory.getPersistService(logType);
        //说明是单独批次合并,需要保证文件不能大于100MB, 否则可能最终生产的文件无法保证接近500MB
        Long fileSize = null;
        if (dto.getExecuteBatch() != null && dto.getExecuteBatch() != 1) {
            fileSize = 100 * 1024 * 1024L;
        }
        List<List<FileMeta>> groupedFiles = GzipUtils.groupFiles(metas, fileSize);
        List<Long> ids = new ArrayList<>();
        List<String> files = new ArrayList<>();
        AtomicInteger index = new AtomicInteger(0);
        List<CompletableFuture<FileMeta>> futures = new ArrayList<>();
        groupedFiles.forEach(fileGroups -> {
            String finalName = String.format(fileName, index.getAndIncrement());
            CompletableFuture<FileMeta> future = mergeFileAndUpload(dto, finalName, mergeType, fileGroups)
                    .whenComplete((meta, ex) -> {
                        if (ex == null) {
                            // 本地合并 并且 totalBatch 不等于1, 知己跳过子批次合并后再上传的步骤
                            if (mergeType == MergeTypeEnum.LOCAL && dto.getTotalBatch() != 1) {
                                OfflineLogDetailInfoDO logDO = OfflineConverters.convertDetail(dto);
                                BeanUtils.copyProperties(meta, logDO);
                                logDO.setMaxTimestamp(maxTimeStamp);
                                persistService.persist(logDO);
                                ids.add(logDO.getId());
                            } else {
                                OfflineLogInfoDO logDO = OfflineConverters.convert(dto);
                                BeanUtils.copyProperties(meta, logDO);
                                logDO.setMaxTimestamp(maxTimeStamp);
                                persistService.persist(logDO);
                                ids.add(logDO.getId());
                            }
                        } else {
                            log.error(ex.getMessage(), ex);
                        }
                        files.addAll(meta.getFiles());
                    });
            futures.add(future);
        });
        return MergeResponse.builder().ids(ids).files(files).futures(futures).build();
    }

    @Builder
    @Getter
    static class MergeResponse {
        private List<Long> ids;

        private List<String> files;

        private List<CompletableFuture<FileMeta>> futures;
    }

    /**
     * @param dto        日志实体
     * @param fileName   合并后的文件名称
     * @param mergeType  合并类型
     * @param fileGroups 待合并的文件信息
     * @return 合并后的文件元信息
     */
    @NotNull
    private CompletableFuture<FileMeta> mergeFileAndUpload(OfflineLogDTO dto, String fileName, MergeTypeEnum mergeType, List<FileMeta> fileGroups) {
        Map<String, String> tags = Map.of("expire", String.format("%dd", dto.getLifeCycle()));
        List<String> fileNames = fileGroups.stream().map(FileMeta::getLogName)
                .collect(Collectors.toList());
        log.debug("开始合并,合并列表:{},合并方式:{}", fileNames, mergeType);
        CompletableFuture<FileMeta> metaData;
        if (mergeType == MergeTypeEnum.OSS) {
            metaData = mergeBYOSS(dto, fileName, fileGroups, tags);
        } else if (mergeType == MergeTypeEnum.LOCAL) {
            metaData = mergeLocal(dto, fileName, fileGroups, tags, false);
        } else {
            metaData = mergeLocal(dto, fileName, fileGroups, tags, true);
        }
        return metaData;
    }

    /**
     * 使用 oss 的方式进行合并,需要保证合并的文件都大于5M(最后一个文件可以不大于)
     *
     * @param dto        日志实体
     * @param fileName   文件名
     * @param fileGroups 待合并的文件
     * @param tags       标签,用于 oss 删除策略
     * @return 文件元数据
     */
    private CompletableFuture<FileMeta> mergeBYOSS(OfflineLogDTO dto, String fileName
            , List<FileMeta> fileGroups, Map<String, String> tags) {
        List<String> fileNames = fileGroups.stream()
                .map(FileMeta::getLogName).collect(Collectors.toList());
        String logType = dto.getLogType();
        BiConsumer<String, List<String>> consumer = (name, files) ->
                fileClient.composeObject(dto.getLogType(), fileName, tags, fileNames);
        return CompletableFuture.supplyAsync(() -> GzipUtils.merge(fileGroups, fileName, consumer), Constant.IO_EXECUTORS)
                .thenApply(meta -> {
                    MetaDTO metaDTO = fileClient.statObject(logType, fileName);
                    meta.setLogName(fileName);
                    meta.setFileSize(metaDTO.getFileSize());
                    return meta;
                });
    }

    /**
     * 使用本地的方式进行合并,可能需要将文件下载到本地
     *
     * @param dto        日志实体
     * @param fileName   文件名
     * @param fileGroups 待合并的文件
     * @param tags       标签,用于 oss 删除策略
     * @param isDownload 是否下载文件到本地
     * @return 文件元数据
     */
    private CompletableFuture<FileMeta> mergeLocal(OfflineLogDTO dto, String fileName, List<FileMeta> fileGroups
            , Map<String, String> tags, Boolean isDownload) {
        String logType = dto.getLogType();
        List<String> files = new CopyOnWriteArrayList<>();
        String localName = configuration.getPath() + "/" + fileName;
        files.add(localName);
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        fileGroups.forEach(item -> {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                if (isDownload) {
                    String objectKey = item.getLogName();
                    String localPath = configuration.getPath() + "/" + objectKey;
                    fileClient.download(logType, objectKey, localPath);
                    item.setLogName(localPath);
                    files.add(localPath);
                } else {
                    files.add(item.getLogName());
                }
            }, Constant.IO_EXECUTORS);
            futures.add(future);
        });
        return CompletableFuture.allOf(futures.toArray(wrap(futures)))
                .thenApply(unused -> {
                    FileMeta metadata = GzipUtils.merge(fileGroups, localName, GzipUtils::mergeGzip);
                    metadata.setFileSize(FileUtil.size(new File(localName)));
                    metadata.setLogName(fileName);
                    fileClient.upload(logType, fileName, localName, Constant.GZIP, tags);
                    metadata.setBucket(logType);
                    metadata.setFiles(files);
                    return metadata;
                });
    }


    public <T> CompletableFuture<?>[] wrap(List<CompletableFuture<T>> futures) {
        if (CollUtil.isEmpty(futures)) {
            CompletableFuture<T> failedFuture = CompletableFuture
                    .failedFuture(new IllegalArgumentException("empty future list"));
            return new CompletableFuture[]{failedFuture};
        }
        return futures.toArray(new CompletableFuture[0]);
    }
}
