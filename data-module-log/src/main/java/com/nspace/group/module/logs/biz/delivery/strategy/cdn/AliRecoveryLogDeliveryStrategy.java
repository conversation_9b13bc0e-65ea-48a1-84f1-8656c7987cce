package com.nspace.group.module.logs.biz.delivery.strategy.cdn;

import cn.hutool.core.thread.ThreadUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.nspace.group.framework.common.enums.BusinessTypeEnum;
import com.nspace.group.module.logs.biz.delivery.context.AliCdnRecoveryContext;
import com.nspace.group.module.infra.design.context.LogContext;
import com.nspace.group.module.infra.design.strategy.LogDeliveryStrategy;
import com.nspace.group.module.logs.service.delivery.DeliveryCdnLogDetailService;
import com.nspace.group.module.logs.service.delivery.dto.DeliveryLogDetailDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.List;

@Slf4j
@Component("aliCdnRecoveryLogDeliveryStrategy")
public class AliRecoveryLogDeliveryStrategy implements LogDeliveryStrategy {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource(name = "aliCdnRecoveryLogSendStrategy")
    private AliRecoveryLogSendStrategy sendStrategy;

    @Resource
    private DeliveryCdnLogDetailService logDetailService;

    @Override
    public void deliver(LogContext logContext) {
        AliCdnRecoveryContext context = (AliCdnRecoveryContext) logContext;
        String targetType = context.getTargetType();
        Integer batchLimit = context.getBatchLimit();
        String taskKey = getLogRecoveryTaskKey(BusinessTypeEnum.BUSINESS_TYPE_CDN.getCode(), targetType);

        int curCount = 0;
        Integer retryCount = context.getRetryCount();
        while (curCount <= batchLimit) {
            //计算Redis key过期时间（秒）
            long taskKeyExpiration = retryCount * 120 * 2;
            Boolean locked = stringRedisTemplate.opsForValue().setIfAbsent(taskKey, StringPool.ONE, Duration.ofSeconds(taskKeyExpiration));
            //获取锁失败直接返回
            if (Boolean.FALSE.equals(locked)) {
                log.warn("fail_to_acquire_lock,task_key={},no_op", taskKey);
                break;
            }
            try {
                int curRetryCount = 1;
                //每次间隔10秒持续retryCount次
                int waitTimeMillis = 10_000;
                while (curRetryCount <= retryCount) {
                    //获取需要重新消费的日志
                    List<DeliveryLogDetailDTO> logDetails = logDetailService.getDetails(targetType, null, batchLimit);
                    context.setLogDetails(logDetails);
                    context.setCurRetryCount(curRetryCount);

                    curCount = logDetails.size();

                    boolean success = sendStrategy.send(context);
                    if (success) {
                        break;
                    }
                    if (curRetryCount != retryCount) {
                        ThreadUtil.sleep(waitTimeMillis);
                    }
                    curRetryCount++;
                }
            } finally {
                //任务执行结束，删除redis中的key
                log.info("remove_task_key_from_registry,task_key={}", taskKey);
                stringRedisTemplate.delete(taskKey);
            }
            if (curCount < batchLimit) break;
        }
    }

    private String getLogRecoveryTaskKey(String bizType, String targetType) {
        return String.join(StringPool.COLON, "delivery_log_recovery_task", bizType, targetType);
    }
}
