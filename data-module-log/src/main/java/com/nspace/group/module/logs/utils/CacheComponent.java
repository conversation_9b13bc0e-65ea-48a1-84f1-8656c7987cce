package com.nspace.group.module.logs.utils;

import com.nspace.group.framework.common.util.date.DateUtils;
import com.nspace.group.module.logs.config.logs.OfflineLogConfiguration;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class CacheComponent {


    @Resource
    private OfflineLogConfiguration configuration;

    @Cacheable(cacheNames = "date-cache", key = "#date+'-'+#zoneId", cacheManager = "caffeine")
    public String getISO8601Date(String date, String zoneId) {
        return DateUtils.convertToISO8601(date, configuration.getOffset(), zoneId);
    }
}
