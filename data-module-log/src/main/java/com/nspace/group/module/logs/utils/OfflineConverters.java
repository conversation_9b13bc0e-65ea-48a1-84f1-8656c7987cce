package com.nspace.group.module.logs.utils;

import com.nspace.group.module.logs.dal.dataobject.offlinelog.OfflineLogDetailInfoDO;
import com.nspace.group.module.logs.dal.dataobject.offlinelog.OfflineLogInfoDO;
import com.nspace.group.module.logs.service.offlinelog.dto.OfflineLogDTO;

public class OfflineConverters {

    public static OfflineLogDetailInfoDO convertDetail(OfflineLogDTO dto) {
        OfflineLogDetailInfoDO entity = new OfflineLogDetailInfoDO();
        entity.setBatchNum(dto.getBatchNum());
        entity.setTenantId(dto.getTenantId());
        entity.setDomain(dto.getDomain());
        entity.setStartTimestamp(dto.getTimestamp());
        entity.setExportType(dto.getExportType());
        entity.setLifeCycle(dto.getLifeCycle());
        entity.setRetryCount(dto.getRetryCount());
        entity.setTotalBatch(dto.getTotalBatch());
        entity.setExecuteBatch(dto.getExecuteBatch());
        return entity;
    }

    public static OfflineLogInfoDO convert(OfflineLogDTO dto) {
        OfflineLogInfoDO entity = new OfflineLogInfoDO();
        entity.setBatchNum(dto.getBatchNum());
        entity.setTenantId(dto.getTenantId());
        entity.setDomain(dto.getDomain());
        entity.setStartTimestamp(dto.getTimestamp());
        entity.setExportType(dto.getExportType());
        entity.setLifeCycle(dto.getLifeCycle());
        entity.setRetryCount(dto.getRetryCount());
        return entity;
    }
}
