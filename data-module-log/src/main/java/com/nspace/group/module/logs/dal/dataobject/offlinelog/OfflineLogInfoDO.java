package com.nspace.group.module.logs.dal.dataobject.offlinelog;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.nspace.group.module.logs.enums.ExportTypeEnum;
import com.nspace.group.module.logs.enums.OfflineStatusEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version :OfflineLogInfoDO.java, v0.1 2024年12月17日 18:23 zhangxin Exp
 */
@Data
public class OfflineLogInfoDO {

    /**
     * ID
     */
    @TableId
    private Long id;

    /**
     * 多租户编号
     */
    private Long tenantId;

    /**
     * 域名
     */
    private String domain;

    /**
     * 批次号
     */
    private String batchNum;

    /**
     * 当前任务的开始时间
     */
    private LocalDateTime startTimestamp;

    /**
     * cur_timestamp 最大值
     */
    private LocalDateTime maxTimestamp;

    /**
     * 文件下载链接
     */
    private String bucket;

    /**
     * 日志名称
     */
    private String logName;

    /**
     * 文件大小，字节
     */
    private Long fileSize;

    /**
     * 状态
     */
    private OfflineStatusEnum status;

    /**
     * 导出类型
     */
    private ExportTypeEnum exportType;

    /**
     * 文件包含的日志行数
     */
    private Integer rows;

    /**
     * 错误信息
     */
    private String message;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 最后更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 失败后重试次数
     */
    private Integer retryCount = 0;
    /**
     * 生命周期,任务执行时的生命周期
     */
    @TableField(value = "lifecycle")
    private Integer lifeCycle;
    /**
     * 是否删除
     */
    @TableLogic
    private Boolean deleted;

}
