package com.nspace.group.module.logs.service.offlinelog.dto;

import com.nspace.group.module.logs.dal.dataobject.offlinelog.OfflineLogDO;
import com.nspace.group.module.logs.enums.ExportTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class OfflineLogDTO {

    private String batchNum;

    private String logType;

    /**
     * 导出类型,全量导出
     */
    private ExportTypeEnum exportType = ExportTypeEnum.FULL;

    private Long tenantId;

    private String domain;

    private LocalDateTime timestamp;

    /**
     * 总分批数量
     */
    private Integer totalBatch;
    /**
     * 当前执行批次,如果 executeBatch 不为空,则执行当前批次,为空,则执行所有批次
     */
    private Integer executeBatch;
    /**
     *
     */
    private Integer retryCount = 0;

    /**
     * 是否需要刷新当前批次的日志
     */
    private Boolean refresh;

    /**
     * 是否需要全量同步
     */
    private Boolean syncAll;

    private Integer lifeCycle = 30;


    public Boolean isRefresh() {
        return refresh;
    }

    public Boolean isSyncAll() {
        return syncAll;
    }


    //创建新对象,避免多线程影响
    public static OfflineLogDTO newDTO(OfflineLogDTO dto) {
        OfflineLogDTO result = new OfflineLogDTO();
        result.setBatchNum(dto.getBatchNum());
        result.setLogType(dto.getLogType());
        result.setTenantId(dto.getTenantId());
        result.setDomain(dto.getDomain());
        result.setExportType(dto.getExportType());
        result.setTimestamp(dto.getTimestamp());
        result.setTotalBatch(dto.getTotalBatch());
        result.setLifeCycle(dto.getLifeCycle());
        result.setRetryCount(dto.getRetryCount());
        return result;
    }

    public static OfflineLogDTO from(OfflineLogDO logDO) {
        OfflineLogDTO dto = new OfflineLogDTO();
        dto.setBatchNum(logDO.getBatchNum());
        dto.setLogType(logDO.getLogType());
        if (logDO.getExportType() != null) {
            dto.setExportType(logDO.getExportType());
        }
        dto.setTenantId(logDO.getTenantId());
        dto.setDomain(logDO.getDomain());
        dto.setTimestamp(logDO.getStartTimestamp());
        dto.setTotalBatch(logDO.getTotalBatch());
        dto.setExecuteBatch(logDO.getExecuteBatch());
        dto.setLifeCycle(logDO.getLifeCycle());
        if (logDO.getRetryCount() != null) {
            dto.setRetryCount(logDO.getRetryCount());
        }
        return dto;
    }
}
