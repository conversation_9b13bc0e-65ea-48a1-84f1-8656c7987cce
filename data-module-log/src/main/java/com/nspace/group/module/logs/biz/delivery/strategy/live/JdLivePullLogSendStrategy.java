package com.nspace.group.module.logs.biz.delivery.strategy.live;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.nspace.group.framework.common.util.json.JsonUtils;
import com.nspace.group.module.infra.client.ApiClient;
import com.nspace.group.module.infra.client.ApiException;
import com.nspace.group.module.infra.client.auth.Authentication;
import com.nspace.group.module.logs.biz.delivery.context.JdLiveContext;
import com.nspace.group.module.infra.design.context.LogContext;
import com.nspace.group.module.infra.design.strategy.LogSendStrategy;
import com.nspace.group.module.logs.client.api.JdLogDeliveryApi;
import com.nspace.group.module.logs.client.impl.auth.JdApiAuth;
import com.nspace.group.module.logs.client.impl.factory.JdApiClientFactory;
import com.nspace.group.module.logs.client.model.JdLiveLogDeliveryResult;
import com.nspace.group.module.logs.convert.delivery.LiveCdnLogConvert;
import com.nspace.group.module.logs.enums.LogDeliveryStatusEnum;
import com.nspace.group.module.logs.service.delivery.LogDeliveryLogDataService;
import com.nspace.group.module.logs.service.delivery.LogDeliveryRecordService;
import com.nspace.group.module.logs.service.delivery.dto.LogDeliveryRecordDTO;
import com.nspace.group.module.logs.service.delivery.dto.LogDeliveryTaskDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class JdLivePullLogSendStrategy implements LogSendStrategy {

    @Resource
    private LogDeliveryRecordService logDeliveryRecordService;

    @Resource
    private LogDeliveryLogDataService logDeliveryLogDataService;


    @Override
    public boolean send(LogContext logContext) {
        JdLiveContext context = (JdLiveContext) logContext;
        LogDeliveryTaskDTO deliveryTask = context.getDeliveryTask();
        send(context, deliveryTask);
        return true;
    }

    private void send(JdLiveContext context, LogDeliveryTaskDTO deliveryTask) {
        String apiBaseUrl = context.getApiBaseUrl();
        String apiPath = context.getApiPath();
        LogDeliveryRecordDTO deliveryRecord = deliveryTask.getDeliveryRecord();
        List<Map<String, Object>> logList = deliveryTask.getLogList();

        int curFailCount = -1;
        int retryCount = 0;
        int retryLimit = context.getRetryCount();

        try {
            byte[] compressedLogData = logDeliveryLogDataService.compress(logList);
            deliveryRecord.setDeliveryStatus(LogDeliveryStatusEnum.INIT.getStatus());
            logDeliveryRecordService.save(deliveryRecord);
            JdLiveLogDeliveryResult deliveryResult;
            //获取ApiClient
            Authentication authentication = new JdApiAuth(apiPath, context.getPrivateKey(), context.getAccessKey());
            ApiClient apiClient = JdApiClientFactory.INSTANCE.createApiClient(apiBaseUrl, authentication);
            //创建JdLogDeliveryApi对象
            JdLogDeliveryApi logDeliveryApi = new JdLogDeliveryApi(apiClient);

            /*
            第一次失败：立即重试
            第二次失败：等待 2 秒
            第三次失败：等待 4 秒
            第四次失败：等待 8 秒
            之后每次间隔10秒直到retryCount次
            */
            while (curFailCount < retryLimit) {
                if (curFailCount > 0) {
                    int waitTimeMillis = curFailCount <= 3 ? (2 << curFailCount) / 2 * 1000 : 10_000;
                    Thread.sleep(waitTimeMillis);
                }
                deliveryResult = sendDeliveryApiCall(logDeliveryApi, compressedLogData);
                String resultJson = JsonUtils.toJsonString(deliveryResult);
                if (deliveryResult.isDeliverySuccessful()) {
                    deliveryRecord.setRetryCount(retryCount);
                    deliveryRecord.setDeliveryStatus(LogDeliveryStatusEnum.SUCCESS.getStatus());
                    deliveryRecord.setDeliveryResult(resultJson);
                    logDeliveryRecordService.save(deliveryRecord);
                    log.info("send,log_delivered,{},apiBaseUrl={},apiPath={}", deliveryRecord, apiBaseUrl, apiPath);
                    break;
                }
                deliveryRecord.setRetryCount(retryCount);
                deliveryRecord.setDeliveryStatus(LogDeliveryStatusEnum.RETRY.getStatus());
                deliveryRecord.setDeliveryResult(resultJson);

                if (curFailCount == retryLimit - 1) {
                    log.info("send,log_delivery_failed,{},apiBaseUrl={},apiPath={},retry_count={}",
                            deliveryRecord, apiBaseUrl, apiPath, retryCount);
                    deliveryRecord.setDeliveryStatus(LogDeliveryStatusEnum.STALE.getStatus());
                    logDeliveryRecordService.save(deliveryRecord);
                    break;
                }

                log.info("send,retrying[{}],{},apiBaseUrl={},apiPath={},result={}",
                        retryCount, deliveryRecord, apiBaseUrl, apiPath, resultJson);
                retryCount++;
                curFailCount++;
            }
        } catch (Exception e) {
            log.error("send,unknown_exception,{},apiBaseUrl={},apiPath={},error={}",
                    deliveryRecord, apiBaseUrl, apiPath, e.getLocalizedMessage());
            deliveryRecord.setDeliveryStatus(LogDeliveryStatusEnum.STALE.getStatus());
            deliveryRecord.setDeliveryResult(ExceptionUtil.getRootCauseMessage(e));
            logDeliveryRecordService.save(deliveryRecord);
        }
    }

    private JdLiveLogDeliveryResult sendDeliveryApiCall(JdLogDeliveryApi logDeliveryApi, byte[] logData) {
        JdLiveLogDeliveryResult deliveryResult;
        try {
            deliveryResult = logDeliveryApi.postLogs(logData);
        } catch (ApiException e) {
            deliveryResult = LiveCdnLogConvert.INSTANCE.getErrorLogDeliveryResult(e);
        }
        return deliveryResult;
    }
}
