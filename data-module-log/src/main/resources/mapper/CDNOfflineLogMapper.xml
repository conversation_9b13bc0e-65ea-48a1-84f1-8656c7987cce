<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nspace.group.module.logs.dal.mapper.offlinelog.CDNOfflineLogMapper">

    <resultMap id="baseResultMap" type="com.nspace.group.module.logs.dal.dataobject.offlinelog.OfflineLogDO">
        <result column="batch_num" property="batchNum"/>
        <result column="log_type" property="logType"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="domain" property="domain"/>
        <result column="total_batch" property="totalBatch"/>
        <result column="execute_batch" property="executeBatch"/>
        <result column="retry_count" property="retryCount"/>
        <result column="start_timestamp" property="startTimestamp"/>
    </resultMap>

    <select id="selectByCurrent" resultMap="baseResultMap">
        SELECT
            ucs.tenant_id,
            cd."domain" AS DOMAIN,
            'cdn' AS log_type,
            COALESCE (ucs.ext_info :: JSONB -> 'offline_log' ->> 'lifecycle', '30') AS lifecycle
        FROM user_cloud_server ucs
            LEFT JOIN cdn_domain cd ON ucs.tenant_id = cd.tenant_id
        WHERE ucs."code" = 'gycdn' AND ucs.deleted = 0 AND cd.deleted = 0
        AND ((ucs.ext_info :: JSONB ->> 'offline_log') = '1' OR (ucs.ext_info :: JSONB -> 'offline_log' ->> 'enable') = 'true');
    </select>

    <select id="getCurrentBatchCount" resultType="long">
        select count(*)
        FROM tb_ods_general_cdn_request_log
        WHERE log_time BETWEEN #{start} AND #{end}
        AND domain = #{domain}  and internal = 0
    </select>


    <select id="selectFailedTask" resultMap="baseResultMap">
        select batch_num, 'cdn' as log_type,tenant_id,"domain",total_batch,execute_batch
        ,max(retry_count) as retry_count,min(start_timestamp) as start_timestamp,max(lifecycle) as lifecycle
        from cdn_offline_log_detail_info
        where status = 'FAILED' and retry_count &lt; 3 and deleted = 0
        group by batch_num,log_type,tenant_id,"domain",total_batch,execute_batch
        union all
        select batch_num, 'cdn' as log_type,tenant_id,"domain",null,null
        ,max(retry_count) as retry_count,min(start_timestamp) as start_timestamp,max(lifecycle) as lifecycle
        from cdn_offline_log_info
        where status = 'FAILED' and retry_count &lt; 3 and deleted = 0
        group by batch_num,log_type,tenant_id,"domain"
    </select>

    <select id="selectMergeTask" resultMap="baseResultMap">
        select batch_num, 'cdn' as log_type,tenant_id,"domain",max(lifecycle) as lifecycle
        from cdn_offline_log_detail_info
        where status = 'SUCCESS' and deleted = 0 and create_time &lt; #{time}
        group by batch_num,log_type,tenant_id,"domain"
    </select>

</mapper>