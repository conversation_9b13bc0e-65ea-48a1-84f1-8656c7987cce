<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nspace.group.module.logs.dal.mapper.offlinelog.ODSOfflineLogMapper">
    <resultMap id="delayedResultMap" type="com.nspace.group.module.logs.dal.dataobject.offlinelog.DelayedOfflineLogDO">
        <result property="batchNum" column="batch_num"/>
        <result property="domain" column="domain"/>
        <result property="syncedCount" column="synced_count"/>
        <result property="unsyncedCount" column="unsyned_count"/>
        <result property="timestamp" column="max_timestamp"/>
    </resultMap>


    <select id="selectCDNDelayedLogs" resultMap="delayedResultMap">
        WITH domain_threshold AS (
        <foreach collection="logs" item="item" separator="UNION ALL">
            SELECT #{item.domain} AS domain,
            FROM_UNIXTIME(#{item.timestamp}) AS threshold
        </foreach>
        )
        SELECT
            hour_floor(log_time, 1) AS batch_num,
            a.domain,
            COUNT(CASE WHEN cur_timestamp &lt;= d.threshold THEN 1 END) AS synced_count,
            COUNT(CASE WHEN cur_timestamp &gt; d.threshold THEN 1 END) AS unsyned_count
        FROM tb_ods_general_cdn_request_log a
        JOIN domain_threshold d ON a.domain = d.domain
        WHERE log_time BETWEEN #{start} AND #{end} and a.domain in
        <foreach collection="logs" item="item" separator="," open="(" close=")">
            #{item.domain}
        </foreach>
        GROUP BY batch_num, a.domain
    </select>

    <select id="selectLiveDelayedLogs" resultMap="delayedResultMap">
        WITH domain_threshold AS (
        <foreach collection="logs" item="item" separator="UNION ALL">
            SELECT  #{item.domain} AS domain,
            FROM_UNIXTIME(#{item.timestamp}) AS threshold
        </foreach>
        )
        SELECT
            hour_floor(log_time, 1) AS batch_num,
            a.domain,
            COUNT(CASE WHEN cur_timestamp &lt;= d.threshold THEN 1 END) AS synced_count,
            COUNT(CASE WHEN cur_timestamp &gt; d.threshold THEN 1 END) AS unsyned_count
        FROM tb_ods_billing_log_stream_pull a
        JOIN domain_threshold d ON a.domain = d.domain
        WHERE log_time BETWEEN #{start} AND #{end} and internal = 0 and a.domain in
        <foreach collection="logs" item="item" separator="," open="(" close=")">
            #{item.domain}
        </foreach>
        GROUP BY batch_num, a.domain
    </select>
</mapper>