<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nspace.group.module.logs.dal.mapper.offlinelog.LiveOfflineLogInfoMapper">

    <resultMap id="delayedResultMap" type="com.nspace.group.module.logs.dal.dataobject.offlinelog.DelayedOfflineLogDO">
        <result property="batchNum" column="batch_num"/>
        <result property="domain" column="domain"/>
        <result property="syncedCount" column="synced_count"/>
        <result property="timestamp" column="max_timestamp"/>
    </resultMap>


    <select id="getHistoryLogs" resultMap="delayedResultMap">
        select batch_num, tenant_id, domain, sum(rows) as synced_count,
        max(max_timestamp) as max_timestamp, max(lifecycle) as lifecycle
        from live_offline_log_info
        where batch_num = #{batchNum} and status ='SUCCESS' and deleted = 0
        group by batch_num,tenant_id,domain
    </select>
</mapper>