package com.nspace.group.module.infra.service.cloudvendor;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.nspace.group.framework.common.enums.BusinessTypeEnum;
import com.nspace.group.framework.common.pojo.PageResult;
import com.nspace.group.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nspace.group.module.infra.convert.domain.DomainConvert;
import com.nspace.group.module.infra.dal.dataobject.cloudvendor.CdnDomainDO;
import com.nspace.group.module.infra.dal.dataobject.cloudvendor.LiveDomainDO;
import com.nspace.group.module.infra.dal.dataobject.cloudvendor.LiveStreamDO;
import com.nspace.group.module.infra.dal.dataobject.cloudvendor.VendorAccountDO;
import com.nspace.group.module.infra.dal.dataobject.dict.DictDataDO;
import com.nspace.group.module.infra.dal.mapper.cloudvendor.CdnDomainMapper;
import com.nspace.group.module.infra.dal.mapper.cloudvendor.LiveDomainMapper;
import com.nspace.group.module.infra.dal.mapper.cloudvendor.LiveStreamMapper;
import com.nspace.group.module.infra.dal.mapper.cloudvendor.VendorAccountMapper;
import com.nspace.group.module.infra.service.cloudvendor.dto.GenericDomainDTO;
import com.nspace.group.module.infra.service.cloudvendor.dto.LiveStreamPageReqDTO;
import com.nspace.group.module.infra.service.cloudvendor.dto.VendorAccountWithDomainsDTO;
import com.nspace.group.module.infra.service.dict.DictDataService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.nspace.group.framework.common.util.collection.CollectionUtils.convertMap;

@Service
public class VendorAccountServiceImpl implements VendorAccountService {

    @Resource
    private VendorAccountMapper vendorAccountMapper;

    @Resource
    private LiveDomainMapper liveDomainMapper;

    @Resource
    private CdnDomainMapper cdnDomainMapper;

    @Resource
    private LiveStreamMapper liveStreamMapper;

    @Resource
    private DictDataService dictDataService;

    @Override
    public List<VendorAccountDO> getAccountList(List<String> platform, String bizType) {
        return vendorAccountMapper.selectList(
                new QueryWrapper<VendorAccountDO>()
                        .eq("deleted", 0)
                        .in("lower(platform)", platform)
                        .eq("biz_type", bizType)
        );
    }

    /**
     * 获取指定租户的账号及关联域名信息
     *
     * @param platform   平台标识
     * @param bizType    业务类型
     * @param domainType 域名类型
     * @return 包含域名信息的云厂商账号列表
     */
    @Override
    public List<VendorAccountWithDomainsDTO> getVendorAccountDomains(List<String> platform, String bizType, String domainType) {
        List<VendorAccountDO> accountList = getAccountList(platform, bizType);
        if (accountList.isEmpty()) {
            return Collections.emptyList();
        }

        List<Long> tenantIdList = accountList.stream()
                .map(VendorAccountDO::getBindTenantId)
                .distinct()
                .collect(Collectors.toList());

        Map<Long, List<GenericDomainDTO>> tenantIdDomainsMap = new HashMap<>();
        if (BusinessTypeEnum.BUSINESS_TYPE_LSS.isSelf(bizType)) {
            tenantIdDomainsMap = liveDomainMapper.selectList(
                    new LambdaQueryWrapper<LiveDomainDO>()
                            .eq(LiveDomainDO::getDeleted, 0)
                            .in(LiveDomainDO::getTenantId, tenantIdList)
                            .eq(StrUtil.isNotBlank(domainType), LiveDomainDO::getType, domainType)
            ).stream().collect(Collectors.groupingBy(LiveDomainDO::getTenantId,
                    Collectors.mapping(DomainConvert.INSTANCE::fromLiveDomainDO, Collectors.toList())));
        } else if (BusinessTypeEnum.BUSINESS_TYPE_CDN.isSelf(bizType)) {
            tenantIdDomainsMap = cdnDomainMapper.selectList(
                    new LambdaQueryWrapper<CdnDomainDO>()
                            .eq(CdnDomainDO::getDeleted, 0)
                            .in(CdnDomainDO::getTenantId, tenantIdList)
                            .eq(StrUtil.isNotBlank(domainType), CdnDomainDO::getType, domainType)
            ).stream().collect(Collectors.groupingBy(CdnDomainDO::getTenantId,
                    Collectors.mapping(DomainConvert.INSTANCE::fromCdnDomainDO, Collectors.toList())));
        }
        Map<Long, List<GenericDomainDTO>> finalTenantIdDomainsMap = tenantIdDomainsMap;
        return accountList.stream()
                .filter(this::isAccountValid)
                .flatMap(account -> {
                    List<GenericDomainDTO> genericDomains = finalTenantIdDomainsMap.getOrDefault(account.getBindTenantId(), Collections.emptyList());
                    return genericDomains.stream()
                            .map(domain -> new VendorAccountWithDomainsDTO(domain.getId(),
                                    account.getAccount(), account.getPlatform(),
                                    account.getSecretId(), account.getSecretKey(),
                                    account.getEndpoint(), account.getBindTenantId(),
                                    domain.getDomain(), account.getExtInfo(), bizType, domain.getType()
                            ));
                })
                .collect(Collectors.toList());
    }

    /**
     * 检查账号信息是否有效
     */
    private boolean isAccountValid(VendorAccountDO account) {
        return account.getBindTenantId() != null
                && account.getSecretId() != null
                && account.getSecretKey() != null
                && account.getEndpoint() != null;
    }

    /**
     * 获取指定平台账号对应直播流信息
     *
     * @param req
     * @return
     */
    @Override
    public PageResult<LiveStreamDO> getVendorAccountStreamList(LiveStreamPageReqDTO req) {
        return liveStreamMapper.selectPage(req);
    }

    /**
     * 获取账号或者域名对应的流信息
     *
     * @param domainType
     * @param tenantId
     * @return
     */
    @Override
    public Map<String, String> getVendorAccountStreamMap(String domainType, Long tenantId) {
        LambdaQueryWrapperX<LiveStreamDO> queryWrapper = new LambdaQueryWrapperX<LiveStreamDO>()
                .eqIfPresent(LiveStreamDO::getDeleted, 0)
                .eqIfPresent(LiveStreamDO::getPlayDomain, domainType)
                .eqIfPresent(LiveStreamDO::getTenantId, tenantId);
        queryWrapper.and(wrapper ->
                wrapper.eq(LiveStreamDO::getStatus, "ONLINE")
                        .or(subWrapper -> subWrapper.eq(LiveStreamDO::getStatus, "OFF")
                                .ge(LiveStreamDO::getEndTime, LocalDateTime.now().minusMinutes(10)))
        );
        List<LiveStreamDO> liveStreamDOS = liveStreamMapper.selectList(queryWrapper);
        if (liveStreamDOS.isEmpty()) {
            return Collections.emptyMap();
        }
        Map<String, String> stringLiveStreamDOMap = convertMap(liveStreamDOS, LiveStreamDO::getStreamName, LiveStreamDO::getAppName);
        return stringLiveStreamDOMap;
    }

    @Override
    public Map<String, Integer> getVendorPlatformNumMap() {
        List<DictDataDO> vendorTypes = dictDataService.getEnabledDictDataListByType("vendor_account_platform_num");
        return vendorTypes.stream().collect(Collectors.toMap(DictDataDO::getLabel,
                dictData -> Integer.valueOf(dictData.getValue()), (v1, v2) -> v1));
    }
}