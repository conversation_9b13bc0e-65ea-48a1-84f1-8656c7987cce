package com.nspace.group.module.infra.dal.dataobject.cdn;


import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 计费用量-通用cdn https请求数量统计
 *
 * <AUTHOR>
 * @since 2025-03-13 17:06:22
 */
@Data
@TableName("tb_usage_general_cdn_https_num")
public class UsageGeneralCdnHttpsNumDO {
    //窗口开始时间
    private LocalDateTime windowStart;
    //租户ID
    private Long tenantId;
    //区域
    private String region;
    //流域名
    private String domain;
    //数据来源
    private Integer dataPlatform;
    //窗口结束时间
    private LocalDateTime windowEnd;
    //https请求数
    private Long httpsNum;
    //计费区域
    private String billingRegion;
    //数据写入时间
    private LocalDateTime curTimestamp;
}

