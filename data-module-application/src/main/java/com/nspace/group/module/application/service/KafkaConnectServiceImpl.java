package com.nspace.group.module.application.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

/**
 * Kafka Connect 任务处理
 * 提供 Kafka Connect 的管理功能，包括获取连接器信息、重启任务等操作。
 */
@Service
@Slf4j
public class KafkaConnectServiceImpl implements KafkaConnectService {

//    @Value("${spring.kafka.bootstrap-servers}")
    private String bootstrapServers;

//    @Value("${spring.kafka.connect.port}")
    private int kafkaConnectPort;

    private final RestTemplate restTemplate;

    public KafkaConnectServiceImpl(RestTemplateBuilder restTemplateBuilder) {
        this.restTemplate = restTemplateBuilder.build();
    }

    /**
     * 随机获取 Kafka Connect 服务 URI
     *
     * @return Kafka Connect URI
     */
    public String getConnectUri() {
        String[] addressArray = bootstrapServers.split(",");
        List<String> hosts = new ArrayList<>();

        for (String address : addressArray) {
            hosts.add(address.contains(":") ? address.split(":")[0] : address);
        }

        if (hosts.isEmpty()) {
            String errorMessage = "Kafka hosts not found in the provided bootstrap servers.";
            log.error(errorMessage);
            throw new IllegalStateException(errorMessage);
        }
        String host = hosts.get(ThreadLocalRandom.current().nextInt(hosts.size()));
        String connectUri = "http://" + host + ":" + kafkaConnectPort;
        return connectUri;
    }

    /**
     * 从 Kafka Connect 获取所有的 Connectors
     *
     * @return Connector 名称列表
     */
    @Override
    public List<String> getAllConnectors() {
        String url = getConnectUri() + "/connectors";
        return fetchData(url, List.class);
    }

    /**
     * 获取指定 Connector 的状态
     *
     * @param connectorName Connector 名称
     * @return Connector 状态
     */
    @Override
    public Map<String, Object> getConnectorStatus(String connectorName) {
        String url = getConnectUri() + "/connectors/" + connectorName + "/status";
        log.info("connector status {}", url);
        return fetchData(url, Map.class);
    }

    /**
     * 通用的 GET 请求处理方法
     */
    private <T> T fetchData(String url, Class<T> responseType) {
        try {
            T result = restTemplate.getForObject(url, responseType);
            if (result instanceof Iterable) {
                log.info("Fetched data details:{}", url);
                for (Object item : (Iterable<?>) result) {
                    log.info(" - {}", item);
                }
            }
            return result != null ? result : (T) new ArrayList<>();
        } catch (Exception e) {
            log.error("Fetching data from {} failed: ", url, e);
            throw new RuntimeException("Failed to fetch data.", e);
        }
    }

    /**
     * 重启指定任务
     *
     * @param connectorName Connector 名称
     * @param taskId        任务 ID
     */
    @Override
    public void restartTask(String connectorName, int taskId) {
        String url = getConnectUri() + "/connectors/" + connectorName + "/tasks/" + taskId + "/restart";
        try {
            restTemplate.postForEntity(url, null, Void.class);
            log.info("Successfully restarted task {} of connector {}", taskId, connectorName);
        } catch (Exception e) {
            log.error("Error restarting task {} of connector {}: ", taskId, connectorName, e);
        }
    }

    /**
     * 监控并重启非运行状态的任务
     */
    @Override
    public void monitorAndRestartConnectors() {
        List<String> connectors = getAllConnectors();
        if (connectors.isEmpty()) {
            log.warn("No connectors found to monitor.");
            return;
        }
        connectors.forEach(connector -> monitorAndRestartConnector(connector));
    }

    /**
     * 监控并重启指定连接器的任务
     */
    private void monitorAndRestartConnector(String connector) {
        try {
            Map<String, Object> status = getConnectorStatus(connector);

            List<Map<String, Object>> tasks = (List<Map<String, Object>>) status.get("tasks");
            if (tasks == null || tasks.isEmpty()) {
                log.warn("No tasks found for connector: {}", connector);
                return;
            }

            tasks.forEach(task -> monitorAndRestartTask(connector, task));
        } catch (Exception e) {
            log.error("Error monitoring connector {}: ", connector, e);
        }
    }

    /**
     * 监控并重启指定任务
     */
    private void monitorAndRestartTask(String connector, Map<String, Object> task) {
        String state = (String) task.get("state");
        int taskId = (int) task.get("id");

        if (!"RUNNING".equalsIgnoreCase(state)) {
            log.warn("connector {} task {} is {}. Restarting...", connector, taskId, state);
            restartTask(connector, taskId);
        } else {
            log.info("connector {} task {} is {}", connector, taskId, state);
        }
    }
}