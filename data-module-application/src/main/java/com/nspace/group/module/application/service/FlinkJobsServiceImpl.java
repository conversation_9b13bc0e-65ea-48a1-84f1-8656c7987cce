package com.nspace.group.module.application.service;

import com.nspace.group.framework.common.util.date.DateUtils;
import com.nspace.group.module.application.dal.dataobject.FlinkJobExecutionRecordDO;
import com.nspace.group.module.application.dal.mapper.FlinkJobExecutionRecordMapper;
import com.nspace.group.module.application.service.dto.FlinkJobResponseDTO;
import com.nspace.group.module.application.utils.RestApiUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * flink任务管理
 */
@Service
@Slf4j
public class FlinkJobsServiceImpl implements FlinkJobsService {

//    @Value("${spring.flink.job_manager.addr}")
    private String jobManagerAddr;

    @Resource
    private RestApiUtils restApiUtils;

    @Resource
    private FlinkJobExecutionRecordMapper flinkJobExecutionRecord;

    private String getJobsUri() {
        return String.format("http://%s/v1/jobs/overview", jobManagerAddr);
    }

    @Override
    public void saveJobsExecutionRecord() {
        String url = getJobsUri();
        FlinkJobResponseDTO allJobs = restApiUtils.fetchData(url, FlinkJobResponseDTO.class);
        if (allJobs == null || allJobs.jobs == null) {
            log.warn("No jobs found from Flink API: {}", url);
            return;
        }
        LocalDateTime now = DateUtils.now();
        for (FlinkJobResponseDTO.FlinkJob job : allJobs.jobs) {
            if(!job.getState().equals("RUNNING")){
                continue;
            }
            FlinkJobExecutionRecordDO record = new FlinkJobExecutionRecordDO();
            record.setJobId(job.getJid());
            record.setJobName(job.getName());
            record.setJobStatus(job.getState());
            record.setCreateTime(DateUtils.convertToLocalDateTime(job.getStartTime()));
            record.setUpdateTime(now);
            flinkJobExecutionRecord.insert(record);
        }
    }
}