package com.nspace.group.scheduler.controller;

import com.nspace.group.framework.file.core.FileClient;
import com.nspace.group.module.logs.enums.ExportTypeEnum;
import com.nspace.group.module.logs.service.offlinelog.DelayedOfflineLogService;
import com.nspace.group.module.logs.service.offlinelog.OfflineLogFactory;
import com.nspace.group.module.logs.service.offlinelog.OfflineLogGenerateService;
import com.nspace.group.module.logs.service.offlinelog.dto.OfflineLogDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Slf4j
@RestController
public class OfflineJobSyncController {


    @Resource
    private OfflineLogGenerateService generateService;


    @Resource
    private OfflineLogFactory offlineLogFactory;
    @Autowired
    private FileClient fileClient;

    @GetMapping("/offline-log/sync")
    public void syncOfflineLog(OfflineLogDTO dto) {
        generateService.generateOfflineLog(dto, true);
    }

    @GetMapping("/offline-log/merge")
    public void mergeOfflineLog(OfflineLogDTO dto) {
        generateService.mergeSubBatch(dto);
    }


    @GetMapping("/offline/list")
    public void statisticsOfflineLog(OfflineLogDTO dto) {
       fileClient.composeObject("live","merge.gz", Collections.emptyMap()
               ,List.of("doris_data_01.gz","doris_data_02.gz"));
    }


    @GetMapping("/offline-log/delayed")
    public void delayedOfflineLog(OfflineLogDTO dto) {
        DelayedOfflineLogService delayedService = offlineLogFactory.getDelayedService(dto.getLogType());
        List<OfflineLogDTO> delayedLogs = delayedService.getDelayedLogs(dto.getBatchNum());
        delayedLogs.forEach(delayedDTO -> {
            if (delayedDTO.isRefresh()) {
                //增量数据的总批次数为1
                delayedDTO.setTotalBatch(1);
                delayedDTO.setExportType(ExportTypeEnum.INCREMENT);
                generateService.generateOfflineLog(delayedDTO, false);
            } else if (delayedDTO.isSyncAll()) {
                generateService.generateOfflineLog(delayedDTO, true);
            }
        });


    }
}
