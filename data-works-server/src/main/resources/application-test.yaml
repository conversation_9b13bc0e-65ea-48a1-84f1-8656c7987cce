server:
  port: 9081
  servlet:
    context-path: /scheduler

--- #################### 数据库相关配置 ####################

spring:
  autoconfigure:
    exclude:
      - com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure # 排除 Druid 自动配置，使用动态数据源
  datasource:
    druid:
      web-stat-filter:
        enabled: true
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        allow: # 留空允许所有 IP 访问
        login-username: # 填入控制台管理用户名
        login-password: # 填入控制台管理密码
      filter:
        stat:
          enabled: true
          log-slow-sql: true
          slow-sql-millis: 100
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true

    dynamic:
      druid:
        initial-size: 1
        min-idle: 1
        max-active: 20
        max-wait: 600000
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 300000
        max-evictable-idle-time-millis: 900000
        validation-query: SELECT 1
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false

      primary: nspace_data_works # 设置默认数据源
      datasource:
        nspace_data_works:
          name: nspace_data_works
          url: jdbc:postgresql://${POSTGRES_ADDR}/${spring.datasource.dynamic.datasource.nspace_data_works.name}
          username: ${POSTGRES_USER}
          password: ${POSTGRES_PWD}
        nspace_controller:
          name: nspace_controller
          url: jdbc:postgresql://${POSTGRES_ADDR}/${spring.datasource.dynamic.datasource.nspace_controller.name}
          username: ${POSTGRES_USER}
          password: ${POSTGRES_PWD}
        nspace_analysis:
          name: nspace_analysis
          url: **********************://${DORIS_ADDR}/${spring.datasource.dynamic.datasource.nspace_analysis.name}
          driver-class-name: com.mysql.cj.jdbc.Driver
          username: ${DORIS_USER}
          password: ${DORIS_PWD}
        nspace_log:
          name: nspace_log
          url: jdbc:postgresql://${POSTGRES_ADDR}/${spring.datasource.dynamic.datasource.nspace_log.name}
          username: ${POSTGRES_USER}
          password: ${POSTGRES_PWD}
        doris_nspace_log:
          name: nspace_log
          url: **********************://${DORIS_ADDR}/${spring.datasource.dynamic.datasource.doris_nspace_log.name}
          driver-class-name: com.mysql.cj.jdbc.Driver
          username: ${DORIS_USER}
          password: ${DORIS_PWD}
        arrow_doris:
          url: jdbc:arrow-flight-sql://${ARROW_DORIS_ADDR}?useServerPrepStmts=false&cachePrepStmts=true&useSSL=false&useEncryption=false&useTimezone=true&serverTimezone=Asia/Shanghai
          driver-class-name: org.apache.arrow.driver.jdbc.ArrowFlightJdbcDriver
          username: ${DORIS_USER}
          password: ${DORIS_PWD}

  redis:
    database: 3
    timeout: 20000
    sentinel:
      master: ${REDIS_SENTINEL_MASTER_NAME}
      nodes: ${REDIS_SENTINEL_REDIS_NODES}
      password: ${REDIS_SENTINEL_REDIS_PASSWORD}
      check-sentinels-list: ${SENTINEL_CHECK_SENTINELS_LIST}

--- #################### 定时任务相关配置 ####################

spring:
  quartz:
    auto-startup: true
    scheduler-name: schedulerName # Scheduler 名字。默认为 schedulerName
    job-store-type: jdbc # Job 存储器类型。默认为 memory 表示内存，可选 jdbc 使用数据库。
    overwrite-existing-jobs: true #启动时更新己存在的Job
    wait-for-jobs-to-complete-on-shutdown: true # 应用关闭时，是否等待定时任务执行完成。默认为 false ，建议设置为 true
    properties: # 添加 Quartz Scheduler 附加属性，更多可以看 http://www.quartz-scheduler.org/documentation/2.4.0-SNAPSHOT/configuration.html 文档
      org:
        quartz:
          # Scheduler 相关配置
          scheduler:
            instanceName: schedulerName
            instanceId: AUTO # 自动生成 instance ID
          # JobStore 相关配置
          jobStore:
            # JobStore 实现类。可见博客：https://blog.csdn.net/weixin_42458219/article/details/122247162
            class: org.springframework.scheduling.quartz.LocalDataSourceJobStore
            isClustered: true # 是集群模式
            clusterCheckinInterval: 15000 # 集群检查频率，单位：毫秒。默认为 15000，即 15 秒
            misfireThreshold: 60000 # misfire 阀值，单位：毫秒。
          # 线程池相关配置
          threadPool:
            threadCount: 20 # 线程池大小。默认为 10 。
            threadPriority: 5 # 线程优先级
            class: org.quartz.simpl.SimpleThreadPool # 线程池类型
    jdbc: # 使用 JDBC 的 JobStore 的时候，JDBC 的配置
      initialize-schema: NEVER # 是否自动使用 SQL 初始化 Quartz 表结构。这里设置成 never ，我们手动创建表结构。

--- #################### 日志配置 ####################
logging:
  file:
    name: ${user.home}/logs/${spring.application.name}.log
  level:
    root: INFO
    #org.springframework: DEBUG # 调试级别日志以便排查问题
    com.nspace.group: INFO

--- #################### 其他相关配置 ####################
spring:
  ipdb: "/opt/nspace/coredns/conf/cz.ipv4.ipdb" # IP 数据库文件路径
  minio:
    endpoint: ${MINIO_ENDPOINT}
    accessKey: ${MINIO_ACCESS_KEY}
    secretKey: ${MINIO_SECRET_KEY}
    domain: ${MINIO_DOMAIN}
  flink:
    job_manager:
          addr: ${FLINK_JOBMANAGER_ADDR}

--- #################### 消息队列相关 ####################
spring:
  kafka:
    bootstrap-servers: ${KAFKA_ADDR} # 指定 Kafka Broker 地址，可以设置多个，以逗号分隔
    connect:
      port: 9083