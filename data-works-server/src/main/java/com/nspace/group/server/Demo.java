package com.nspace.group.server;

import com.nspace.group.module.logs.utils.GzipUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@Slf4j
public class Demo {

    public static void main(String[] args) {
        GzipUtils.mergeGzip("merge.zip",List.of(
                "/tmp/logs/offline/24dbbf99-2253-46d9-99f8-a56f8f341147-0.gz",
                "/tmp/logs/offline/8453d71b-460a-42e2-a4a7-70064a2572a9_00.gz"
        ));

    }
}
